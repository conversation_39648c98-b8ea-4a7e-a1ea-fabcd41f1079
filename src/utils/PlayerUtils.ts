import {
  calculateBestPosition as calculatePlayerBestPosition,
  CorePlayer,
  Player,
} from '../models/player';

export const STAMINA_SCALAR = 5.8;

// Calculate best position for any player type
export function calculatePlayerPosition(player: CorePlayer): string[] {
  return calculatePlayerBestPosition(player); // Now we can use the same function for both types
}

// Format player value to display format
export function formatPlayerValue(value: number): string {
  if (value < 1000000) {
    return `${(value / 1000).toFixed(0)}K`;
  }
  return `${(value / 1000000).toFixed(1)}M`;
}

export function calculateCurrentEnergy(stamina: number, energy: number, lastMatchPlayed: number) {
  const hoursSinceLastMatch = (Date.now() - lastMatchPlayed) / (1000 * 60 * 60);
  const staminaScalar = stamina * STAMINA_SCALAR;
  return Math.floor(Math.min(100, energy + staminaScalar * hoursSinceLastMatch));
}

export function calculateEnergyByNextMatch(
  stamina: number,
  energy: number,
  lastMatchPlayed: number,
  nextMatchTime: number
) {
  if (nextMatchTime === 0) {
    return 100;
  }
  const hoursBetweenMatches = (nextMatchTime - lastMatchPlayed) / (1000 * 60 * 60);
  const staminaScalar = stamina * STAMINA_SCALAR;
  return Math.floor(Math.min(100, energy + staminaScalar * hoursBetweenMatches));
}
