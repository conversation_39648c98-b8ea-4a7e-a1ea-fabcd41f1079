import { ScoutedPlayer } from '../hooks/useQueries';
import { Manager } from '../models/manager';
import { Player, TransferListPlayer } from '../models/player';
import { Team } from '../models/team';

export interface CachedPlayerData {
  teamPlayers: Player[];
  transferListPlayers: TransferListPlayer[];
  scoutedPlayers: ScoutedPlayer[];
  myBidsPlayers: TransferListPlayer[];
}

export interface DataCache {
  manager: Manager | null;
  team: Team | null;
  players: CachedPlayerData;
  lastUpdated: {
    manager: number;
    team: number;
    players: number;
  };
}

export const createEmptyCache = (): DataCache => ({
  manager: null,
  team: null,
  players: {
    teamPlayers: [],
    transferListPlayers: [],
    scoutedPlayers: [],
    myBidsPlayers: [],
  },
  lastUpdated: {
    manager: 0,
    team: 0,
    players: 0,
  },
});

/**
 * Update a player in the cache across all player arrays
 */
export const updatePlayerInCache = (
  cache: DataCache,
  updatedPlayer: Partial<Player> & { playerId: string }
): DataCache => {
  const updatePlayerInArray = <T extends Player | TransferListPlayer | ScoutedPlayer>(
    players: T[]
  ): T[] =>
    players.map((player) =>
      player.playerId === updatedPlayer.playerId ? { ...player, ...updatedPlayer } : player
    );

  return {
    ...cache,
    team: cache.team
      ? {
          ...cache.team,
          players: updatePlayerInArray(cache.team.players),
        }
      : cache.team,
    players: {
      teamPlayers: updatePlayerInArray(cache.players.teamPlayers),
      transferListPlayers: updatePlayerInArray(cache.players.transferListPlayers),
      scoutedPlayers: updatePlayerInArray(cache.players.scoutedPlayers),
      myBidsPlayers: updatePlayerInArray(cache.players.myBidsPlayers),
    },
    lastUpdated: {
      ...cache.lastUpdated,
      players: Date.now(),
    },
  };
};

/**
 * Update manager data in cache
 */
export const updateManagerInCache = (
  cache: DataCache,
  updatedManager: Partial<Manager>
): DataCache => ({
  ...cache,
  manager: cache.manager ? { ...cache.manager, ...updatedManager } : cache.manager,
  lastUpdated: {
    ...cache.lastUpdated,
    manager: Date.now(),
  },
});

/**
 * Update team data in cache
 */
export const updateTeamInCache = (cache: DataCache, updatedTeam: Partial<Team>): DataCache => ({
  ...cache,
  team: cache.team ? { ...cache.team, ...updatedTeam } : cache.team,
  lastUpdated: {
    ...cache.lastUpdated,
    team: Date.now(),
  },
});

/**
 * Find a player across all cached player arrays
 */
export const findPlayerInCache = (
  cache: DataCache,
  playerId: string
): Player | TransferListPlayer | ScoutedPlayer | null => {
  // Check team players first
  if (cache.team) {
    const teamPlayer = cache.team.players.find((p) => p.playerId === playerId);
    if (teamPlayer) return teamPlayer;
  }

  // Check other player arrays
  const { teamPlayers, transferListPlayers, scoutedPlayers, myBidsPlayers } = cache.players;

  return (
    teamPlayers.find((p) => p.playerId === playerId) ||
    transferListPlayers.find((p) => p.playerId === playerId) ||
    scoutedPlayers.find((p) => p.playerId === playerId) ||
    myBidsPlayers.find((p) => p.playerId === playerId) ||
    null
  );
};

/**
 * Check if cache data is stale (older than specified minutes)
 */
export const isCacheStale = (lastUpdated: number, maxAgeMinutes: number = 5): boolean => {
  const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
  return Date.now() - lastUpdated > maxAge;
};
