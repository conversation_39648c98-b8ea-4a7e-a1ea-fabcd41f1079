import { MatchStats } from '../components/MatchStatsDisplay';

export interface Scorer {
  playerId: string;
  playerName: string;
  team: number;
  goalTime: {
    minute: number;
    half: number;
  }[];
}

export interface Fixture {
  fixtureId: string;
  homeTeamId: string;
  homeTeamName: string;
  awayTeamId: string;
  awayTeamName: string;
  date: number;
  score?: [number, number];
  scorers?: Scorer[];
}

export interface PossibleEventSubstitution {
  team?: string;
  homeTeam?: string;
  oppTeam?: string;
  awayTeam?: string;
  player?: string;
  oppPlayer?: string;
  nextPlayer?: string;
  homeScore?: string;
  awayScore?: string;
}

export interface MatchEvent {
  localisationId: string;
  substitutions: PossibleEventSubstitution;
  minute: number;
  half: number;
}

export interface DBFixture {
  gameworldId_leagueId: string;
  gameworldId: string;
  leagueId: string;
  fixtureId: string;
  homeTeam: {
    teamId: string;
    teamName: string;
  };
  awayTeam: {
    teamId: string;
    teamName: string;
  };
  date: number;
  stats?: MatchStats;
  events?: MatchEvent[];
  played: boolean;
  simulatedAt?: number;
  seed?: number;
  score?: [number, number];
}
