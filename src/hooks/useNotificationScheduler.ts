import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect } from 'react';
import { Platform } from 'react-native';
import {
  cancelAllMatchNotifications,
  configureNotifications,
  scheduleAllMatchNotifications,
} from '../services/NotificationScheduler';
import { logger } from '../utils/logger';

/**
 * Hook to manage notification scheduling based on user preferences
 */
export const useNotificationScheduler = () => {
  useEffect(() => {
    // Configure notification behaviour when the hook mounts
    configureNotifications();
  }, []);

  /**
   * Update notification scheduling based on current preferences
   */
  const updateNotificationScheduling = async () => {
    if (Platform.OS === 'web') return;
    // Cancel all notifications before rescheduling
    await cancelAllMatchNotifications();

    try {
      // Check if pre-match push notifications are enabled
      const preMatchPushEnabled = await AsyncStorage.getItem('@notification_preMatch_push');

      if (preMatchPushEnabled === 'true') {
        logger.log('Pre-match notifications enabled, scheduling notifications');
        await scheduleAllMatchNotifications();
      } else {
        logger.log('Pre-match notifications disabled, cancelling notifications');
        await cancelAllMatchNotifications();
      }
    } catch (error) {
      logger.error('Error updating notification scheduling:', error);
    }
  };

  return {
    updateNotificationScheduling,
  };
};
