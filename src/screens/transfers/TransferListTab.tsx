import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import PlayerDetailView from '../../components/PlayerDetailView';
import TransferListPlayerRow from '../../components/PlayerRow/TransferListPlayerRow';
import PositionFilter, {
  PositionFilter as PositionFilterType,
} from '../../components/PositionFilter/PositionFilter';
import {
  ButtonText,
  Container,
  EmptyListContainer,
  EmptyListText,
  ListContainer,
  ListHeaderText,
  LoadingContainer,
  LoadMoreButton,
} from '../../components/TransferSharedStyles';
import { useManager } from '../../context/ManagerContext';
import {
  useCachedMyBidsPlayers,
  useCachedTeam,
  useCachedTransferListPlayers,
} from '../../hooks/useCachedData';
import { CorePlayer, TransferListPlayer } from '../../models/player';
import { logger } from '../../utils/logger';

const EmptyListContainerStyled = styled(EmptyListContainer)`
  padding: 20px;
`;

const TransferListTab = () => {
  const { manager } = useManager();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastEvaluatedKey, setLastEvaluatedKey] = useState<string | undefined>(undefined);
  const [transferListPlayers, setTransferListPlayers] = useState<TransferListPlayer[]>([]);
  const [myBidsPlayers, setMyBidsPlayers] = useState<TransferListPlayer[]>([]);
  const [selectedPlayer, setSelectedPlayer] = useState<TransferListPlayer | null>(null);
  const [positionFilter, setPositionFilter] = useState<PositionFilterType>('All');
  // Track if we've reached the end of the list
  const [reachedEnd, setReachedEnd] = useState(false);

  // Log manager info for debugging
  useEffect(() => {
    logger.log('Manager info:', manager);
    logger.log('GameworldId:', manager?.gameworldId);
  }, [manager]);
  const [teamAverages, setTeamAverages] = useState<
    Record<PositionFilterType, Record<string, number>>
  >({
    All: {},
    Goalkeeper: {},
    Defender: {},
    Midfielder: {},
    Attacker: {},
  });

  const { data: transferListData, isLoading: isLoadingTransferList } = useCachedTransferListPlayers(
    manager?.gameworldId,
    lastEvaluatedKey
  );

  const { data: myBidsData, isLoading: isLoadingMyBids } = useCachedMyBidsPlayers(
    manager?.gameworldId
  );

  const { team: teamData, isLoading: isLoadingTeam } = useCachedTeam(
    manager?.gameworldId,
    manager?.team?.teamId
  );

  // Helper function to get the pagination key from various possible locations
  const getPaginationKey = useCallback(
    (data: any): string | undefined => {
      if (!data) return undefined;
      if (reachedEnd) return undefined; // Don't return a key if we've reached the end

      // Check in pagination object
      if (data.pagination) {
        if (data.pagination.lastEvaluatedKey) return data.pagination.lastEvaluatedKey;
        if (data.pagination.nextToken) return data.pagination.nextToken;
        if (data.pagination.pageId) return data.pagination.pageId;
      }

      // Check at root level
      if (data.lastEvaluatedKey) return data.lastEvaluatedKey;
      if (data.nextToken) return data.nextToken;
      if (data.pageId) return data.pageId;

      return undefined;
    },
    [reachedEnd]
  );

  // Track if this is the initial load or a pagination load
  const isInitialLoad = React.useRef(true);

  // Effect for handling my bids data
  useEffect(() => {
    if (myBidsData) {
      logger.log('My bids data received:', myBidsData);
      const players = myBidsData.players || [];
      setMyBidsPlayers(players);
    }
  }, [myBidsData]);

  useEffect(() => {
    if (transferListData) {
      logger.log('Transfer list data received:', transferListData);
      logger.log('Current lastEvaluatedKey:', lastEvaluatedKey);
      logger.log('Is initial load:', isInitialLoad.current);
      logger.log('Is loading more:', isLoadingMore);

      // Check if players property exists, if not, try to handle different response structures
      const players = transferListData.players || [];

      // Check if we've reached the end of the list
      const paginationKey = getPaginationKey(transferListData);
      if (isLoadingMore && players.length === 0) {
        logger.log('Received empty page, reached end of list');
        setReachedEnd(true);
        return;
      }

      if (isInitialLoad.current || (!lastEvaluatedKey && !isLoadingMore)) {
        // This is the initial load
        logger.log('Setting initial players:', players.length);
        setTransferListPlayers(players);
        isInitialLoad.current = false;

        // Reset reached end flag on initial load
        setReachedEnd(false);
      } else if (isLoadingMore || lastEvaluatedKey) {
        // This is a pagination load
        logger.log('Adding more players:', players.length);
        setTransferListPlayers((prev) => {
          if (!Array.isArray(prev)) {
            logger.warn('Previous players is not an array:', prev);
            return players;
          }
          if (!Array.isArray(players)) {
            logger.warn('New players is not an array:', players);
            return prev;
          }

          // Filter out duplicates before combining
          const existingPlayerIds = new Set(prev.map((player) => player.playerId));
          const uniqueNewPlayers = players.filter(
            (player) => !existingPlayerIds.has(player.playerId)
          );

          logger.log('Existing players:', prev.length);
          logger.log('New unique players:', uniqueNewPlayers.length);

          // If we got no new unique players, we've reached the end
          if (uniqueNewPlayers.length === 0 && isLoadingMore) {
            logger.log('No new unique players, reached end of list');
            setReachedEnd(true);
          }

          // Combine previous and unique new players
          const combinedPlayers = [...prev, ...uniqueNewPlayers];
          logger.log('Combined players count:', combinedPlayers.length);
          return combinedPlayers;
        });
      }
    }
  }, [transferListData, isLoadingMore, lastEvaluatedKey, getPaginationKey]);

  // Log pagination info for debugging
  useEffect(() => {
    if (transferListData) {
      const paginationKey = getPaginationKey(transferListData);
      logger.log('Pagination key found:', paginationKey);
      if (paginationKey) {
        logger.log('More players available, can load more');
      } else {
        logger.log('No more players available');
      }
    }
  }, [getPaginationKey, transferListData]);

  useEffect(() => {
    if (teamData?.players) {
      const calculatePositionAverages = (players: CorePlayer[], attributes: string[]) => {
        const result: Record<string, number> = {};

        attributes.forEach((attr) => {
          const values = players.map(
            (player) => player.attributes[attr as keyof typeof player.attributes] || 0
          );
          const sum = values.reduce((acc, val) => acc + val, 0);
          result[attr] = values.length > 0 ? sum / values.length : 0;
        });

        return result;
      };

      const averages: Record<PositionFilterType, Record<string, number>> = {
        All: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
          'tackling',
          'marking',
          'heading',
          'passing',
          'vision',
          'ballControl',
          'finishing',
          'pace',
          'crossing',
        ]),
        Goalkeeper: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
        ]),
        Defender: calculatePositionAverages(teamData.players, ['tackling', 'marking', 'heading']),
        Midfielder: calculatePositionAverages(teamData.players, [
          'passing',
          'vision',
          'ballControl',
        ]),
        Attacker: calculatePositionAverages(teamData.players, ['finishing', 'pace', 'crossing']),
      };

      setTeamAverages(averages);
    }
  }, [teamData?.players]);

  // Function to handle position filter selection
  const handlePositionSelect = (position: PositionFilterType) => {
    setPositionFilter(position);
  };

  const loadMorePlayers = () => {
    // Don't try to load more if we've reached the end
    if (reachedEnd) {
      logger.log('Already reached end of list, not loading more');
      return;
    }

    const paginationKey = getPaginationKey(transferListData);

    if (paginationKey && paginationKey !== lastEvaluatedKey) {
      logger.log('Loading more players with key:', paginationKey);
      logger.log('Previous key was:', lastEvaluatedKey);
      setIsLoadingMore(true); // Set loading more state to true
      setLastEvaluatedKey(paginationKey);
    } else if (paginationKey === lastEvaluatedKey) {
      logger.log('Already using this pagination key, not loading more');
    } else {
      logger.log('No more players to load');
      setReachedEnd(true); // Mark that we've reached the end
    }
  };

  // Function to check if the user's team is the highest bidder
  const isUserHighestBidder = (player: TransferListPlayer): boolean => {
    if (!manager?.team || !player.bidHistory || player.bidHistory.length === 0) {
      return false;
    }

    // Sort bid history by maximum bid (highest first)
    const sortedBids = [...player.bidHistory].sort((a, b) => b.maximumBid - a.maximumBid);

    // Check if the user's team is the highest bidder
    return sortedBids[0]?.teamId === manager.team.teamId;
  };

  const renderPlayer = ({ item }: { item: TransferListPlayer; index: number }) => {
    const handlePlayerSelect = (player: TransferListPlayer) => {
      setSelectedPlayer(player);
    };

    return (
      <TransferListPlayerRow
        player={item}
        onSelect={handlePlayerSelect}
        isSelected={selectedPlayer?.playerId === item.playerId}
        positionFilter={positionFilter}
        teamAverages={teamAverages[positionFilter]}
        isHighestBidder={isUserHighestBidder(item)}
      />
    );
  };

  const renderEmptyList = () => (
    <EmptyListContainerStyled>
      <EmptyListText>No players currently available on the transfer list.</EmptyListText>
    </EmptyListContainerStyled>
  );

  // Update loading states based on query loading states
  useEffect(() => {
    logger.log('Loading states:', {
      isLoadingTransferList,
      isLoadingMyBids,
      isLoadingTeam,
      isLoadingMore,
    });

    // Only set isLoading to true for initial load, not for pagination
    if (!isLoadingMore) {
      setIsLoading(isLoadingTransferList || isLoadingMyBids || isLoadingTeam);
    }

    // If we were loading more and the loading has finished, reset the loading more state
    if (isLoadingMore && !isLoadingTransferList) {
      logger.log('Finished loading more, resetting isLoadingMore');
      setIsLoadingMore(false);
    }
  }, [isLoadingTransferList, isLoadingMyBids, isLoadingTeam, isLoadingMore]);

  if (isLoading) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  const renderEmptyMyBidsList = () => (
    <EmptyListContainer>
      <EmptyListText>You haven't placed any bids yet.</EmptyListText>
    </EmptyListContainer>
  );

  // Create a combined data structure with sections
  const combinedData: ListItem[] = [];

  // Add my bids section if there are any bids
  if (myBidsPlayers.length > 0) {
    combinedData.push({
      data: "Players You've Bid On",
      type: 'header',
    });
    combinedData.push(
      ...myBidsPlayers.map((player) => ({
        data: player,
        type: 'myBids' as const,
      }))
    );
  }

  // Add transfer list section
  combinedData.push({
    data: 'Players Available for Transfer',
    type: 'header',
  });
  combinedData.push(
    ...transferListPlayers.map((player) => ({
      data: player,
      type: 'transferList' as const,
    }))
  );

  return (
    <Container>
      <PositionFilter positionFilter={positionFilter} onPositionSelect={handlePositionSelect} />

      {/* Combined List with Sections */}
      <ListContainer style={Platform.OS === 'web' ? { position: 'relative', zIndex: 1 } : {}}>
        <FlatList<ListItem>
          data={combinedData}
          renderItem={({ item }) => {
            if (item.type === 'header') {
              return (
                <ListHeaderText style={{ marginTop: 16, marginBottom: 8 }}>
                  {item.data as string}
                </ListHeaderText>
              );
            }

            return renderPlayer({ item: item.data as TransferListPlayer, index: 0 });
          }}
          keyExtractor={(item, index) => {
            if (item.type === 'header') return `${item.type}-${index}`;
            return (item.data as TransferListPlayer).playerId;
          }}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListFooterComponent={() =>
            getPaginationKey(transferListData) ? (
              <LoadMoreButton onPress={loadMorePlayers} disabled={isLoadingMore}>
                {isLoadingMore ? (
                  <>
                    <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
                    <ButtonText>Loading...</ButtonText>
                  </>
                ) : (
                  <ButtonText>Load More</ButtonText>
                )}
              </LoadMoreButton>
            ) : null
          }
        />
      </ListContainer>

      {/* Player detail view rendered outside of ListContainer for proper z-index handling */}
      {selectedPlayer && (
        <PlayerDetailView player={selectedPlayer} onClose={() => setSelectedPlayer(null)} />
      )}
    </Container>
  );
};

export default TransferListTab;
