import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Skeleton } from 'moti/skeleton';
import React from 'react';
import { FlatList, Image, Modal, Platform, TouchableOpacity, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { RewardsResponse } from '../models/rewards';
import { useTheme } from '../theme/ThemeContext';
import { formatPlayerValue } from '../utils/PlayerUtils';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.ScrollView`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const Header = styled.View`
  padding: 40px 20px 20px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-bottom: 10px;
  align-items: center;
  justify-content: center;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const TeamName = styled(Text)`
  font-size: 24px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 5px;
`;

const TeamBalance = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
`;

const BalanceRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
`;

const BalanceIcon = styled.Image`
  width: 24px;
  height: 24px;
  margin-right: 6px;
`;

const ContentContainer = styled.View`
  padding: 20px;
`;

const IconGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20px;
`;

const IconContainer = styled.TouchableOpacity`
  width: 45%;
  aspect-ratio: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 10px;
  margin: 10px 0;
  justify-content: center;
  align-items: center;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const IconText = styled(Text)`
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ThemeButton = styled.TouchableOpacity`
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 10px;
  z-index: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 20px;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

type MenuItem = {
  name: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  href: string;
};

const HomeScreen = () => {
  const router = useRouter();
  const { isDark, themePreference, setThemePreference, theme } = useTheme();
  const { team, loading } = useManager();
  const [isAlertVisible, setAlertVisible] = React.useState(false);

  const [rewards, setRewards] = React.useState<any[][]>([]);
  const [loginStreak, setLoginStreak] = React.useState<number>(0);
  const [rewardDay, setRewardDay] = React.useState<number>(0);
  const [isRewardModalVisible, setRewardModalVisible] = React.useState(false);
  const [_rewardsLoading, setRewardsLoading] = React.useState(true);

  React.useEffect(() => {
    let useMock = false;
    // MOCK RESPONSE FOR TESTING - REMOVE THIS BLOCK TO RESTORE REAL API
    if (useMock) {
      setRewards([
        [{ type: 'scoutToken', value: 1 }],
        [
          { type: 'money', value: 100000 },
          { type: 'scoutToken', value: 1 },
        ],
        [
          { type: 'magicSponge', value: 1 },
          { type: 'scoutToken', value: 1 },
        ],
        [
          { type: 'redCardAppeal', value: 1 },
          { type: 'scoutToken', value: 1 },
        ],
        [
          { type: 'magicSponge', value: 1 },
          { type: 'scoutToken', value: 1 },
        ],
        [
          { type: 'trainingBoost', value: 1 },
          { type: 'scoutToken', value: 1 },
        ],
        [
          { type: 'money', value: 1000000 },
          { type: 'scoutToken', value: 1 },
        ],
      ]);
      setLoginStreak(1);
      setRewardDay(0);
      setRewardModalVisible(true);
      setRewardsLoading(false);
    } else {
      if (!loading) {
        (async () => {
          try {
            const res = await callApi<RewardsResponse>('/manager/rewards');

            logger.debug('rewards response', res);
            if (res.status !== 202) {
              setRewards(res.rewards || []);
              setLoginStreak(res.loginStreak || 0);
              setRewardDay(res.day || 0);
              setRewardModalVisible(true);
            }
          } catch (error) {
            // Optionally handle error
            logger.error('Failed to fetch rewards', error);
          } finally {
            setRewardsLoading(false);
          }
        })();
      }
    }
  }, [loading]);

  const handleThemePress = () => {
    setAlertVisible(true);
  };

  const menuItems: MenuItem[] = [
    { name: 'Team', icon: 'group', href: '/team' },
    { name: 'League', icon: 'emoji-events', href: '/league' },
    { name: 'Fixtures', icon: 'event', href: '/fixtures' },
    { name: 'Transfers', icon: 'swap-horiz', href: '/transfers' },
    { name: 'Training', icon: 'fitness-center', href: '/training' },
    { name: 'Finances', icon: 'account-balance-wallet', href: '/finances' },
    { name: 'Profile', icon: 'person', href: '/manager-profile' },
    { name: 'Notifications', icon: 'notifications-active', href: '/notification-settings' },
  ];

  return (
    <Container>
      <ThemeButton onPress={handleThemePress}>
        <MaterialIcons
          name={
            themePreference === 'system'
              ? 'settings-brightness'
              : isDark
                ? 'light-mode'
                : 'dark-mode'
          }
          size={24}
          color={theme.colors.text.primary}
        />
      </ThemeButton>

      <Header>
        {loading ? (
          <Skeleton.Group show={true}>
            <Skeleton width={180} height={32} radius={8} colorMode={isDark ? 'dark' : 'light'} />
            <Skeleton width={120} height={22} radius={6} colorMode={isDark ? 'dark' : 'light'} />
          </Skeleton.Group>
        ) : (
          <>
            <TeamName>{team?.teamName || 'Your Team'}</TeamName>
            {__DEV__ && team?.teamId && (
              <Text
                style={{
                  fontSize: 12,
                  color: theme.colors.text.secondary,
                  textAlign: 'center',
                  marginBottom: 2,
                }}
              >
                Team ID: {team.teamId}
              </Text>
            )}
            <BalanceRow>
              <BalanceIcon source={require('../../assets/quid.svg')} />
              <TeamBalance>{formatPlayerValue(team?.balance ?? 0)}</TeamBalance>
            </BalanceRow>
          </>
        )}
      </Header>

      <ContentContainer>
        <IconGrid>
          {menuItems.map((item) => (
            <IconContainer
              key={item.name}
              onPress={loading ? undefined : () => router.push(item.href)}
              activeOpacity={loading ? 1 : 0.7}
              disabled={loading}
              style={loading ? { opacity: 0.5 } : {}}
            >
              {loading ? (
                <Skeleton
                  width={40}
                  height={40}
                  radius={20}
                  colorMode={isDark ? 'dark' : 'light'}
                />
              ) : (
                <MaterialIcons name={item.icon} size={40} color={theme.colors.text.primary} />
              )}
              {loading ? (
                <Skeleton width={80} height={18} radius={6} colorMode={isDark ? 'dark' : 'light'} />
              ) : (
                <IconText>{item.name}</IconText>
              )}
            </IconContainer>
          ))}
        </IconGrid>
      </ContentContainer>

      <Modal visible={isRewardModalVisible} transparent animationType="slide">
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0,0,0,0.5)',
          }}
        >
          <View
            style={{
              flex: 1,
              width: '100%',
              backgroundColor: theme.colors.surface,
              borderRadius: 0,
              paddingTop: 48,
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}
          >
            <Text style={{ fontSize: 22, fontWeight: 'bold', marginBottom: 8 }}>Daily Rewards</Text>
            <Text style={{ marginBottom: 16 }}>Current Streak: {loginStreak}</Text>
            <FlatList
              data={rewards}
              keyExtractor={(_, idx) => idx.toString()}
              numColumns={2}
              contentContainerStyle={{
                width: '100%',
                paddingHorizontal: 8,
                paddingBottom: 16,
              }}
              columnWrapperStyle={{
                justifyContent: 'space-between',
                width: '100%',
              }}
              renderItem={({ item, index }) => {
                const isDay7 = index === 6;
                return (
                  <View
                    style={{
                      marginVertical: 8,
                      padding: isDay7 ? 24 : 16,
                      borderRadius: 14,
                      backgroundColor:
                        index === rewardDay ? theme.colors.primary : theme.colors.background,
                      borderWidth: index === rewardDay ? 2 : 1,
                      borderColor: index === rewardDay ? theme.colors.primary : theme.colors.border,
                      alignItems: 'center',
                      width: isDay7 ? '98%' : '48%',
                      alignSelf: isDay7 ? 'center' : 'auto',
                      minHeight: isDay7 ? 120 : 90,
                      justifyContent: 'center',
                    }}
                  >
                    {item.map((reward: any, i: number) =>
                      reward.type === 'scoutToken' ? (
                        <View
                          key={i}
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginBottom: 6,
                            width: isDay7 ? 56 : 120,
                            height: isDay7 ? 56 : 120,
                            position: 'relative',
                          }}
                        >
                          <Image
                            source={require('../../assets/scoutToken.png')}
                            style={{
                              width: '100%',
                              height: '100%',
                              resizeMode: 'contain',
                            }}
                          />
                          <Text
                            style={{
                              position: 'absolute',
                              right: 4,
                              bottom: 2,
                              color:
                                index === rewardDay
                                  ? theme.colors.text.primary
                                  : theme.colors.text.primary,
                              fontWeight: 'bold',
                              fontSize: isDay7 ? 22 : 18,
                              textShadowColor: '#000',
                              textShadowOffset: { width: 1, height: 1 },
                              textShadowRadius: 2,
                            }}
                          >
                            x{reward.value}
                          </Text>
                        </View>
                      ) : (
                        <Text
                          key={i}
                          style={{
                            color:
                              index === rewardDay
                                ? theme.colors.text.primary
                                : theme.colors.text.primary,
                            fontSize: isDay7 ? 20 : 16,
                            marginBottom: 2,
                          }}
                        >
                          {reward.type === 'money'
                            ? `💰`
                            : reward.type === 'magicSponge'
                              ? `🧽`
                              : reward.type === 'redCardAppeal'
                                ? `🟥`
                                : reward.type === 'trainingBoost'
                                  ? `💪`
                                  : ''}{' '}
                          {reward.value}
                        </Text>
                      )
                    )}
                    <Text
                      style={{
                        fontSize: isDay7 ? 16 : 12,
                        color:
                          index === rewardDay
                            ? theme.colors.text.primary
                            : theme.colors.text.secondary,
                        marginTop: 4,
                        fontWeight: isDay7 ? 'bold' : 'normal',
                      }}
                    >
                      Day {index + 1}
                    </Text>
                  </View>
                );
              }}
              style={{ marginBottom: 16, width: '100%' }}
              showsVerticalScrollIndicator={false}
            />
            <TouchableOpacity
              style={{
                backgroundColor: theme.colors.primary,
                borderRadius: 8,
                paddingVertical: 14,
                paddingHorizontal: 48,
                marginTop: 8,
                marginBottom: 32,
              }}
              onPress={() => setRewardModalVisible(false)}
            >
              <Text style={{ color: theme.colors.text.primary, fontWeight: 'bold', fontSize: 18 }}>
                Collect
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <CrossPlatformAlert
        visible={isAlertVisible}
        title="Theme Settings"
        message="Choose your preferred theme"
        buttons={[
          {
            text: 'System',
            onPress: () => setThemePreference('system'),
            style: themePreference === 'system' ? 'destructive' : 'default',
          },
          {
            text: 'Light',
            onPress: () => setThemePreference('light'),
            style: themePreference === 'light' ? 'destructive' : 'default',
          },
          {
            text: 'Dark',
            onPress: () => setThemePreference('dark'),
            style: themePreference === 'dark' ? 'destructive' : 'default',
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]}
        onDismiss={() => setAlertVisible(false)}
      />
    </Container>
  );
};

export default HomeScreen;
