import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import ScoutingTab from './transfers/ScoutingTab';
import TransferListTab from './transfers/TransferListTab';

interface StyledProps {
  theme: DefaultTheme;
}

interface TabProps extends StyledProps {
  isActive: boolean;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const TabContainer = styled.View`
  flex-direction: row;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.primary};
`;

const Tab = styled(TouchableOpacity)<TabProps>`
  flex: 1;
  padding: 16px;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: ${(props) => (props.isActive ? props.theme.colors.primary : 'transparent')};
`;

const TabText = styled(Text)<TabProps>`
  font-family: 'NunitoBold';
  font-size: 16px;
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.primary : props.theme.colors.text.secondary};
`;

const ContentContainer = styled.View`
  flex: 1;
`;

const TransfersScreen = () => {
  const [activeTab, setActiveTab] = useState<'scouting' | 'transfer-list'>('scouting');

  const renderContent = () => {
    switch (activeTab) {
      case 'scouting':
        return <ScoutingTab />;
      case 'transfer-list':
        return <TransferListTab />;
      default:
        return null;
    }
  };

  return (
    <Container>
      <TabContainer>
        <Tab isActive={activeTab === 'scouting'} onPress={() => setActiveTab('scouting')}>
          <TabText isActive={activeTab === 'scouting'}>SCOUTING</TabText>
        </Tab>
        <Tab isActive={activeTab === 'transfer-list'} onPress={() => setActiveTab('transfer-list')}>
          <TabText isActive={activeTab === 'transfer-list'}>TRANSFER LIST</TabText>
        </Tab>
      </TabContainer>
      <ContentContainer>{renderContent()}</ContentContainer>
    </Container>
  );
};

export default TransfersScreen;
