import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { Text } from '../components/Text';
import { MANAGER_ID_KEY, useManager } from '../context/ManagerContext';
import { Manager } from '../models/manager';
import { useTheme } from '../theme/ThemeContext';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 20px;
`;

const LoadingText = styled(Text)`
  font-size: 18px;
  margin-top: 20px;
  text-align: center;
`;

const SubText = styled(Text)`
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  max-width: 300px;
`;

const FootballAnimation = styled(View)`
  margin-bottom: 30px;
`;

const TeamAssignmentScreen = () => {
  const { theme } = useTheme();
  const [checkCount, setCheckCount] = useState(0);
  const [loadingText, setLoadingText] = useState('Assigning you a team');
  const [subText, setSubText] = useState('This should only take a moment...');
  const { refreshManager, manager } = useManager();

  // Function to check if manager has been assigned a team
  const checkManagerAssignment = useCallback(async () => {
    try {
      // Call the manager API
      await refreshManager();
    } catch (error) {
      logger.error('Error checking manager assignment:', error);
    }
  }, [refreshManager]);

  useEffect(() => {
    async function checkTeamAssigned() {
      // Check if manager has a team assigned
      if (manager && manager.managerId && manager.team) {
        // Save manager ID to AsyncStorage
        await AsyncStorage.setItem(MANAGER_ID_KEY, manager.managerId);

        // Manager has a team, navigate to home screen
        router.replace('/');
      }
    }
    checkTeamAssigned();
  }, [manager]);

  // Update loading text based on how long the user has been waiting
  useEffect(() => {
    const messages = [
      {
        count: 0,
        message: 'Assigning you a team',
        subMessage: 'This should only take a moment...',
      },
      {
        count: 3,
        message: 'Still working on it',
        subMessage: 'Finding the perfect team for you...',
      },
      { count: 6, message: 'Almost there', subMessage: 'Preparing your squad...' },
      {
        count: 10,
        message: 'Taking a bit longer than usual',
        subMessage: 'The transfer committee is being thorough...',
      },
      {
        count: 15,
        message: 'Thanks for your patience',
        subMessage: 'Good managers are worth waiting for...',
      },
    ];

    // Find the appropriate message based on check count
    const messageObj = [...messages].reverse().find((m) => checkCount >= m.count);
    if (messageObj) {
      setLoadingText(messageObj.message);
      setSubText(messageObj.subMessage);
    }
  }, [checkCount]);

  // Poll the API every 5 seconds
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let mounted = true;

    const pollManagerStatus = async () => {
      await checkManagerAssignment();

      if (mounted) {
        if (!manager) {
          // Increment check count for message updates
          setCheckCount((prev) => prev + 1);
        } else {
          // Clear interval if manager is assigned
          clearInterval(intervalId);
        }
      }
    };

    // Initial check
    pollManagerStatus();

    // Set up polling interval
    intervalId = setInterval(pollManagerStatus, 5000);

    // Clean up on unmount
    return () => {
      mounted = false;
      clearInterval(intervalId);
    };
  }, []);

  // Simple football animation component
  const FootballLoader = () => (
    <FootballAnimation>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </FootballAnimation>
  );

  return (
    <Container>
      <FootballLoader />
      <LoadingText>{loadingText}</LoadingText>
      <SubText>{subText}</SubText>
    </Container>
  );
};

export default TeamAssignmentScreen;
