import dayjs from 'dayjs';
import React from 'react';
import { ActivityIndicator } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import MatchEventDisplay from '../components/MatchEventDisplay';
import MatchScoreDisplay from '../components/MatchScoreDisplay';
import MatchStatsDisplay from '../components/MatchStatsDisplay';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { useFixtureDetails } from '../hooks/useQueries';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.ScrollView`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const HeaderSection = styled.View`
  padding: 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  margin-bottom: 16px;
`;

const DateText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
  margin-bottom: 8px;
`;

const FixtureDetailScreen = ({ fixtureId }: { fixtureId: string }) => {
  const { manager, team } = useManager();
  const { data, isLoading } = useFixtureDetails(manager?.gameworldId, team?.league.id, fixtureId);

  if (isLoading || !data) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  const { fixture, commentary, homeTeamPlayers, awayTeamPlayers } = data;
  const scorers = fixture.stats?.scorers || [];

  return (
    <Container>
      <HeaderSection>
        <DateText>{dayjs(fixture.date).format('dddd D MMMM YYYY HH:mm')}</DateText>
        <MatchScoreDisplay
          homeTeamName={fixture.homeTeam.teamName}
          awayTeamName={fixture.awayTeam.teamName}
          score={fixture.stats?.score}
          scorers={scorers}
          isLarge={true}
        />
      </HeaderSection>

      {fixture.stats && <MatchStatsDisplay stats={fixture.stats} />}

      {fixture.events && fixture.events.length > 0 && (
        <MatchEventDisplay
          events={fixture.events}
          commentary={commentary}
          homeTeamName={fixture.homeTeam.teamName}
          awayTeamName={fixture.awayTeam.teamName}
          homePlayers={homeTeamPlayers}
          awayPlayers={awayTeamPlayers}
        />
      )}
    </Container>
  );
};

export default FixtureDetailScreen;
