import React from 'react';
import { ActivityIndicator, ScrollView } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { useLeague } from '../hooks/useQueries';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

interface TableRowProps extends StyledProps {
  isPromotion?: boolean;
  isRelegation?: boolean;
}

interface CellProps extends StyledProps {
  flex?: number;
  center?: boolean;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const TableContainer = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  overflow: hidden;
`;

const TableRow = styled.View<TableRowProps>`
  flex-direction: row;
  padding: 12px 8px;
  background-color: ${({ isPromotion, isRelegation, theme }: TableRowProps) =>
    isPromotion ? '#5d5dc5' : isRelegation ? '#9c2e2e' : theme.colors.surface};
`;

const HeaderRow = styled.View`
  flex-direction: row;
  padding: 12px 8px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.primary};
`;

const Cell = styled(Text)<CellProps>`
  flex: ${({ flex }: CellProps) => flex || 1};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  text-align: ${({ center }: CellProps) => (center ? 'center' : 'left')};
  font-family: 'Nunito';
`;

const HeaderCell = styled(Cell)`
  font-family: 'NunitoBold';
`;

const Title = styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-bottom: 8px;
`;

const Tier = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-bottom: 16px;
`;

const LeagueScreen = () => {
  const { manager, team } = useManager();
  logger.log('LeagueScreen', manager, team);
  const { data: leagueData, isLoading } = useLeague(manager?.gameworldId, team?.league.id);

  if (isLoading) {
    return (
      <Container>
        <ActivityIndicator size="large" />
      </Container>
    );
  }

  if (!leagueData) {
    return (
      <Container>
        <Title>No data available</Title>
      </Container>
    );
  }

  const sortedTeams = [...leagueData.league.teams].sort((a, b) => {
    const goalDifferenceA = a.goalsFor - a.goalsAgainst;
    const goalDifferenceB = b.goalsFor - b.goalsAgainst;

    if (a.points !== b.points) {
      return b.points - a.points;
    } else if (goalDifferenceA !== goalDifferenceB) {
      return goalDifferenceB - goalDifferenceA;
    } else if (a.draws !== b.draws) {
      return b.draws - a.draws;
    } else {
      return a.teamName.localeCompare(b.teamName);
    }
  });

  const { promotionSpots, relegationSpots } = leagueData.league.leagueRules;

  return (
    <Container>
      <Title>{leagueData.league.name}</Title>
      <Tier>Tier {leagueData.league.tier}</Tier>
      <ScrollView>
        <TableContainer>
          <HeaderRow>
            <HeaderCell flex={3}>Team Name</HeaderCell>
            <HeaderCell center>P</HeaderCell>
            <HeaderCell center>W</HeaderCell>
            <HeaderCell center>D</HeaderCell>
            <HeaderCell center>L</HeaderCell>
            <HeaderCell center>GF</HeaderCell>
            <HeaderCell center>GA</HeaderCell>
            <HeaderCell center>GD</HeaderCell>
            <HeaderCell center>Pts</HeaderCell>
          </HeaderRow>
          {sortedTeams.map((t, index) => (
            <TableRow
              key={t.teamName}
              isPromotion={index < promotionSpots}
              isRelegation={index >= sortedTeams.length - relegationSpots}
            >
              <Cell flex={3}>{t.teamName}</Cell>
              <Cell center>{t.played}</Cell>
              <Cell center>{t.wins}</Cell>
              <Cell center>{t.draws}</Cell>
              <Cell center>{t.losses}</Cell>
              <Cell center>{t.goalsFor}</Cell>
              <Cell center>{t.goalsAgainst}</Cell>
              <Cell center>{t.goalsFor - t.goalsAgainst}</Cell>
              <Cell center>{t.points}</Cell>
            </TableRow>
          ))}
        </TableContainer>
      </ScrollView>
    </Container>
  );
};

export default LeagueScreen;
