import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { SchedulableTriggerInputTypes } from 'expo-notifications/build/Notifications.types';
import { Platform } from 'react-native';
import { logger } from '../utils/logger';

// Storage keys for scheduled notification identifiers
const MORNING_NOTIFICATION_ID_KEY = '@morning_notification_id';
const EVENING_NOTIFICATION_ID_KEY = '@evening_notification_id';

// Notification content
const NOTIFICATION_TITLE = 'Match Reminder';
const NOTIFICATION_BODY = 'The next match starts in 30 minutes. Make sure you pick your best team!';

/**
 * Configure notification behaviour
 */
export const configureNotifications = () => {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: false,
      shouldShowBanner: true,
      shouldShowList: false,
    }),
  });
};

/**
 * Schedule a daily notification at a specific time
 */
const scheduleDailyNotification = async (
  hour: number,
  minute: number,
  identifier: string
): Promise<string | null> => {
  try {
    // Check if notifications are permitted
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      logger.log('Notification permissions not granted');
      return null;
    }

    // Cancel any existing notification with this identifier
    await Notifications.cancelScheduledNotificationAsync(identifier);

    // Calculate the next occurrence of the specified time
    const now = new Date();
    const scheduledTime = new Date();
    scheduledTime.setHours(hour, minute, 0, 0);

    // If the time has already passed today, schedule for tomorrow
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    // Schedule the notification
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: NOTIFICATION_TITLE,
        body: NOTIFICATION_BODY,
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: {
        hour,
        minute,
        type: SchedulableTriggerInputTypes.DAILY,
      },
    });

    logger.log(`Scheduled daily notification at ${hour}:${minute} with ID: ${notificationId}`);
    return notificationId;
  } catch (error) {
    logger.error('Error scheduling notification:', error);
    return null;
  }
};

/**
 * Schedule morning notification (9:30 AM)
 */
export const scheduleMorningNotification = async (): Promise<void> => {
  const notificationId = await scheduleDailyNotification(9, 30, 'morning-match-reminder');
  if (notificationId) {
    await AsyncStorage.setItem(MORNING_NOTIFICATION_ID_KEY, notificationId);
  }
};

/**
 * Schedule evening notification (7:30 PM)
 */
export const scheduleEveningNotification = async (): Promise<void> => {
  const notificationId = await scheduleDailyNotification(19, 30, 'evening-match-reminder');
  if (notificationId) {
    await AsyncStorage.setItem(EVENING_NOTIFICATION_ID_KEY, notificationId);
  }
};

/**
 * Cancel morning notification
 */
export const cancelMorningNotification = async (): Promise<void> => {
  try {
    const notificationId = await AsyncStorage.getItem(MORNING_NOTIFICATION_ID_KEY);
    if (notificationId) {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      await AsyncStorage.removeItem(MORNING_NOTIFICATION_ID_KEY);
      logger.log('Cancelled morning notification');
    }
  } catch (error) {
    logger.error('Error cancelling morning notification:', error);
  }
};

/**
 * Cancel evening notification
 */
export const cancelEveningNotification = async (): Promise<void> => {
  try {
    const notificationId = await AsyncStorage.getItem(EVENING_NOTIFICATION_ID_KEY);
    if (notificationId) {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      await AsyncStorage.removeItem(EVENING_NOTIFICATION_ID_KEY);
      logger.log('Cancelled evening notification');
    }
  } catch (error) {
    logger.error('Error cancelling evening notification:', error);
  }
};

/**
 * Cancel all scheduled match notifications
 */
export const cancelAllMatchNotifications = async (): Promise<void> => {
  await Notifications.cancelAllScheduledNotificationsAsync();

  console.log(
    'Cancelled all match notifications',
    Notifications.getAllScheduledNotificationsAsync()
  );
};

/**
 * Schedule all match notifications based on user preferences
 */
export const scheduleAllMatchNotifications = async (): Promise<void> => {
  try {
    if (Platform.OS === 'web') return;

    // Check if pre-match push notifications are enabled
    const preMatchPushEnabled = await AsyncStorage.getItem('@notification_preMatch_push');

    if (preMatchPushEnabled === 'true') {
      logger.log('Scheduling match notifications...');
      await Promise.all([scheduleMorningNotification(), scheduleEveningNotification()]);
    } else {
      logger.log('Pre-match push notifications disabled, not scheduling');
      await cancelAllMatchNotifications();
    }
  } catch (error) {
    logger.error('Error managing match notifications:', error);
  }
};

/**
 * Schedule a test notification (for debugging - triggers in 10 seconds)
 */
export const scheduleTestNotification = async (): Promise<void> => {
  try {
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      logger.log('Notification permissions not granted');
      return;
    }

    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Test Match Reminder',
        body: 'This is a test notification. The next match starts in 30 minutes. Make sure you pick your best team!',
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: {
        seconds: 10,
        type: SchedulableTriggerInputTypes.TIME_INTERVAL,
      },
    });

    logger.log(`Scheduled test notification with ID: ${notificationId}`);
  } catch (error) {
    logger.error('Error scheduling test notification:', error);
  }
};

/**
 * Get all scheduled notifications (for debugging)
 */
export const getScheduledNotifications = async () => {
  try {
    const notifications = await Notifications.getAllScheduledNotificationsAsync();
    logger.log('Scheduled notifications:', notifications);
    return notifications;
  } catch (error) {
    logger.error('Error getting scheduled notifications:', error);
    return [];
  }
};
