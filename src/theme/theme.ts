export interface Theme {
  colors: {
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
    };
    primary: string;
    shadow: string;
    error: string;
    warning: string;
    success: string;
    border: string;
  };
  typography: {
    regular: string;
    bold: string;
  };
}

export const lightTheme: Theme = {
  colors: {
    background: '#FFFFFF',
    surface: '#F5F5F5',
    text: {
      primary: '#242424',
      secondary: 'rgba(0, 0, 0, 0.87)',
    },
    primary: '#2196F3',
    shadow: 'rgba(0, 0, 0, 0.1)',
    error: '#F44336',
    success: '#4CAF50',
    border: '#BDBDBD',
    warning: '#FFA500',
  },
  typography: {
    regular: 'Nunito',
    bold: 'NunitoBold',
  },
};

export const darkTheme: Theme = {
  colors: {
    background: '#242424',
    surface: '#1e1e1e',
    text: {
      primary: 'rgba(255, 255, 255, 0.87)',
      secondary: 'rgba(255, 255, 255, 0.60)',
    },
    primary: '#64B5F6',
    shadow: 'rgba(0, 0, 0, 0.3)',
    error: '#FF5252',
    success: '#81C784',
    border: '#424242',
    warning: '#FFA500',
  },
  typography: {
    regular: 'Nunito',
    bold: 'NunitoBold',
  },
};
