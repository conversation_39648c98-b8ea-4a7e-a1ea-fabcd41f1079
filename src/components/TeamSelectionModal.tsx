import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import type { LeagueSummary } from '../hooks/useLeagues';
import { League } from '../models/league';
import { logger } from '../utils/logger';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface SelectableItemProps extends StyledProps {
  isSelected: boolean;
}

interface Team {
  teamId: string;
  teamName: string;
  leagueId: string;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-height: 80%;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const TierHeader = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 18px;
  padding: 8px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
`;

const LeagueHeader = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 16px;
  padding: 8px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface + '80'};
`;

const TeamItem = styled.TouchableOpacity<SelectableItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '40' : 'transparent'};
`;

const TeamName = styled(Text)<SelectableItemProps>`
  font-family: ${(props) => (props.isSelected ? 'NunitoBold' : 'Nunito-Regular')};
  font-size: 16px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const Button = styled.TouchableOpacity`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
`;

const ButtonText = styled(Text)`
  color: ${(props: StyledProps) => props.theme.colors.background};
  font-family: 'NunitoBold';
`;

const LoadingContainer = styled.View`
  padding: 20px;
  align-items: center;
`;

const CostRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  gap: 12px;
`;

const CostIcon = styled.Image`
  width: 24px;
  height: 24px;
  margin-right: 4px;
`;

const CostText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

interface Props {
  visible: boolean;
  leagues: LeagueSummary[];
  onClose: () => void;
  onSelect: (teamId: string) => void;
}

const TeamSelectionModal: React.FC<Props> = ({ visible, leagues, onClose, onSelect }) => {
  const [selectedTeamId, setSelectedTeamId] = useState<string | null>(null);
  const [selectedLeagueId, setSelectedLeagueId] = useState<string | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch teams when a league is selected
  useEffect(() => {
    const fetchTeams = async () => {
      if (!selectedLeagueId) {
        setTeams([]);
        return;
      }

      setLoading(true);
      try {
        // Get the gameworld ID from the first league (they should all have the same gameworld ID)
        const gameworldId = leagues[0]?.gameworld;

        if (!gameworldId) return;

        const response = await callApi<League>(
          `/${gameworldId}/leagues/${selectedLeagueId}?includeTeams=true`
        );
        if (response.league && response.league.teams) {
          setTeams(
            response.league.teams.map((team: any) => ({
              teamId: team.teamId,
              teamName: team.teamName,
              leagueId: team.leagueId,
            }))
          );
        }
      } catch (error) {
        logger.error('Failed to fetch teams:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, [selectedLeagueId, leagues]);

  const groupedLeagues = React.useMemo(() => {
    if (!leagues || !Array.isArray(leagues)) {
      return [];
    }

    const grouped = leagues.reduce(
      (acc, league) => {
        if (!acc[league.tier]) {
          acc[league.tier] = [];
        }
        acc[league.tier].push(league);
        return acc;
      },
      {} as Record<number, LeagueSummary[]>
    );

    return Object.entries(grouped)
      .sort(([tierA], [tierB]) => Number(tierA) - Number(tierB))
      .flatMap(([tier, leagueList]) => [
        { type: 'tier', tier: Number(tier) },
        ...leagueList.map((league) => ({ type: 'league', ...league })),
      ]);
  }, [leagues]);

  const renderItem = ({ item }: { item: any }) => {
    if (item.type === 'tier') {
      return <TierHeader>Tier {item.tier}</TierHeader>;
    }

    if (item.type === 'league') {
      const isExpanded = selectedLeagueId === item.id;
      return (
        <>
          <LeagueHeader
            onPress={() => {
              if (isExpanded) {
                setSelectedLeagueId(null);
                setTeams([]);
                setSelectedTeamId(null);
              } else {
                setSelectedLeagueId(item.id);
                setSelectedTeamId(null);
              }
            }}
          >
            {item.name} {isExpanded ? '▼' : '▶'}
          </LeagueHeader>
          {isExpanded &&
            (loading ? (
              <LoadingContainer>
                <ActivityIndicator size="large" />
              </LoadingContainer>
            ) : (
              teams.map((team) => (
                <TeamItem
                  key={team.teamId}
                  isSelected={selectedTeamId === team.teamId}
                  onPress={() => setSelectedTeamId(team.teamId)}
                >
                  <TeamName isSelected={selectedTeamId === team.teamId}>{team.teamName}</TeamName>
                </TeamItem>
              ))
            ))}
        </>
      );
    }

    return null;
  };

  const handleConfirm = () => {
    if (selectedTeamId) {
      onSelect(selectedTeamId);
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Select Team to Scout</ModalTitle>

          <CostRow>
            <CostIcon source={require('../../assets/scoutToken.png')} />
            <CostText>x 1</CostText>
            <CostIcon source={require('../../assets/icon.png')} />
            <CostText>x 5,000</CostText>
          </CostRow>

          <FlatList
            data={groupedLeagues}
            renderItem={renderItem}
            keyExtractor={(item) =>
              item.type === 'tier' ? `tier-${item.tier}` : (item as LeagueSummary).id
            }
          />

          <ButtonContainer>
            <Button onPress={onClose}>
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button
              onPress={handleConfirm}
              style={{ opacity: selectedTeamId ? 1 : 0.5 }}
              disabled={!selectedTeamId}
            >
              <ButtonText>Scout!</ButtonText>
            </Button>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};

export default TeamSelectionModal;
