import { goBack, navigate } from 'expo-router/build/global-state/routing';
import React from 'react';
import { Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { Player } from '../../models/player';
import { Text } from '../Text';
import { usePlayerActions } from './hooks/usePlayerActions';

interface StyledProps {
  theme: DefaultTheme;
}

interface ReleasePlayerModalProps {
  player: Player;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: left;
  flex: 1;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-family: 'Nunito';
  font-size: 16px;
  margin-bottom: 8px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  flex: 1;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

export const ReleasePlayerModal: React.FC<ReleasePlayerModalProps> = ({ player }) => {
  const { playerActionsState, playerActions } = usePlayerUtils();
  const handleReleasePlayer = () => {
    playerActions.handleReleasePlayer(player, () => {
      playerActions.setIsReleaseModalVisible(false);
    });
  };
  const isReleasing = playerActionsState.isReleasing;

  return (
    <Modal
      visible={playerActionsState.isReleaseModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => playerActions.setIsReleaseModalVisible(false)}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Release Player</ModalTitle>

          <InputContainer>
            <Label>
              Are you sure you want to release {player.firstName} {player.surname}?
            </Label>
            <Label style={{ color: '#e3172a', fontFamily: 'NunitoBold', marginTop: 10 }}>
              This action cannot be undone. The player will be removed from your team permanently.
            </Label>
          </InputContainer>

          <ButtonContainer>
            <TransferButton
              onPress={() => playerActions.setIsReleaseModalVisible(false)}
              disabled={isReleasing}
              style={{ backgroundColor: '#888' }}
            >
              <ButtonText>Cancel</ButtonText>
            </TransferButton>
            <TransferButton
              onPress={handleReleasePlayer}
              disabled={isReleasing}
              style={{ backgroundColor: '#e3172a' }}
            >
              <ButtonText>{isReleasing ? 'Releasing...' : 'Release Player'}</ButtonText>
            </TransferButton>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
