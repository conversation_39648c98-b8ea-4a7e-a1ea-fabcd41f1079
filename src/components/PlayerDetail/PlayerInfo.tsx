import React from 'react';
import { Image } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { CorePlayer } from '../../models/player';
import { Team } from '../../models/team';
import { Text } from '../Text';
import { useMagicSponge } from './hooks/useMagicSponge';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerInfoProps {
  player: CorePlayer;
  team?: Team;
  playerStatus: PlayerStatus;
}

const InfoContainer = styled.View`
  flex-direction: row;
  margin-bottom: 24px;
`;

const PlayerImageContainer = styled.View`
  width: 120px;
  height: 120px;
  margin-right: 16px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f0f0f0;
`;

const PlayerStatsContainer = styled.View`
  flex: 1;
  justify-content: center;
`;

const DetailText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-bottom: 8px;
`;

const MagicSpongeButton = styled.TouchableOpacity`
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 18px;
  padding: 2px;
  z-index: 10;
`;

export const PlayerInfo: React.FC<PlayerInfoProps> = ({ player, team, playerStatus }) => {
  const { formattedValue, currentEnergy, energyByNextMatch, isInjured, isPlayerInUserTeam } =
    playerStatus;

  const [, magicSpongeActions] = useMagicSponge();
  const showMagicSponge = isPlayerInUserTeam && (currentEnergy! < 100 || isInjured);

  return (
    <InfoContainer>
      <PlayerImageContainer>
        <Image
          source={require('../../../assets/mugshot.png')}
          style={{ width: '100%', height: '100%' }}
          resizeMode="cover"
        />
        {showMagicSponge && (
          <MagicSpongeButton onPress={() => magicSpongeActions.setIsSpongeModalVisible(true)}>
            <Image
              source={require('../../../assets/magicSponge.png')}
              style={{ width: 36, height: 36 }}
            />
          </MagicSpongeButton>
        )}
      </PlayerImageContainer>
      <PlayerStatsContainer>
        <DetailText>{`Age: ${player.age}`}</DetailText>
        <DetailText>{`Value: ${formattedValue}`}</DetailText>
        {!playerStatus.isAuctionPlayer && <DetailText>{`Energy: ${currentEnergy}%`}</DetailText>}
        {team && team.nextFixture && energyByNextMatch !== undefined && (
          <DetailText>{`Energy by Next Match: ${energyByNextMatch}%`}</DetailText>
        )}
      </PlayerStatsContainer>
    </InfoContainer>
  );
};
