import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useDataCache } from '../../../context/DataCacheContext';
import { useManager } from '../../../context/ManagerContext';
import { formatPlayerValue } from '../../../utils/PlayerUtils';
import { logger } from '../../../utils/logger';

import { goBack } from 'expo-router/build/global-state/routing';
import { Player, TransferListPlayer } from '../../../models/player';

export interface AlertMessage {
  title: string;
  message: string;
}

export interface PlayerActionsState {
  isOfferModalVisible: boolean;
  offerAmount: string;
  isSubmitting: boolean;
  showAlert: boolean;
  alertCloseAction: (() => void) | undefined;
  alertMessage: AlertMessage;
  maxBid: number | null;
  isHighestBidder: boolean;
  isReleaseModalVisible: boolean;
  isReleasing: boolean;
}

export interface PlayerActionsActions {
  setIsOfferModalVisible: (visible: boolean) => void;
  setOfferAmount: (amount: string) => void;
  setShowAlert: (show: boolean) => void;
  setAlertCloseAction: (action: (() => void) | undefined) => void;
  setIsReleaseModalVisible: (visible: boolean) => void;
  handleSubmitOffer: (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number
  ) => Promise<void>;
  handleReleasePlayer: (player: Player, onClose: () => void) => Promise<void>;
  prefillOfferAmount: (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number
  ) => void;
}

export const usePlayerActions = (): [PlayerActionsState, PlayerActionsActions] => {
  const { manager, team: userTeam, updateTeam } = useManager();
  const { updatePlayer } = useDataCache();

  const [isOfferModalVisible, setIsOfferModalVisible] = useState(false);
  const [offerAmount, setOfferAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState<AlertMessage>({ title: '', message: '' });
  const [alertCloseAction, setAlertCloseAction] = useState<() => void | undefined>();
  const [maxBid, setMaxBid] = useState<number | null>(null);
  const [isHighestBidder, setIsHighestBidder] = useState<boolean>(false);
  const [isReleaseModalVisible, setIsReleaseModalVisible] = useState(false);
  const [isReleasing, setIsReleasing] = useState(false);

  const showAlertMessage = (title: string, message: string) => {
    setAlertMessage({ title, message });
    setShowAlert(true);
  };

  const handleSubmitOffer = async (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number
  ) => {
    if (!offerAmount || isNaN(Number(offerAmount))) {
      showAlertMessage('Invalid Amount', 'Please enter a valid transfer amount.');
      return;
    }

    // For auction players, validate bid amount
    if (isAuctionPlayer) {
      const bidAmount = Number(offerAmount);
      const minBidRequired = currentBid + 1000;

      if (bidAmount < minBidRequired) {
        showAlertMessage(
          'Bid Too Low',
          `Your bid must be at least ${formatPlayerValue(minBidRequired)} (current bid + £1K).`
        );
        return;
      }

      // Check if auction has ended
      if (player.auctionEndTime < Date.now()) {
        showAlertMessage('Auction Ended', 'This auction has already ended.');
        return;
      }
    }

    try {
      setIsSubmitting(true);

      if (isAuctionPlayer) {
        // Submit bid for auction player
        const bidResponse = await callApi<{ maxBid: number; highestBidder: boolean }>(
          `/transfer/bid`,
          {
            method: 'POST',
            body: JSON.stringify({
              player: player.playerId,
              maxBid: Number(offerAmount),
              myTeam: manager?.team?.teamId,
            }),
          }
        );

        // Process the response to get maxBid and highestBidder status
        setMaxBid(bidResponse.maxBid);
        setIsHighestBidder(bidResponse.highestBidder);

        // Update the player in cache with new bid information if it's a TransferListPlayer

        const updatedBidHistory = player.bidHistory || [];
        const newBid = {
          teamId: manager?.team?.teamId || '',
          teamName: manager?.team?.teamName || 'Your Team',
          maximumBid: bidResponse.maxBid,
          bidTime: Date.now(),
        };

        updatePlayer({
          playerId: player.playerId,
          bidHistory: [...updatedBidHistory, newBid],
          auctionCurrentPrice: Math.max(player.auctionCurrentPrice, bidResponse.maxBid),
        });

        setIsOfferModalVisible(false);
        showAlertMessage(
          'Success',
          `Bid submitted successfully! ${bidResponse.highestBidder ? 'You are the highest bidder!' : 'You are not the highest bidder.'}`
        );
      } else {
        // Submit transfer offer for team player
        await callApi(`/transfer/offer`, {
          method: 'POST',
          body: JSON.stringify({
            player: player.playerId,
            offer: Number(offerAmount),
            theirTeam: player.teamId,
            myTeam: manager?.team?.teamId,
          }),
        });

        setIsOfferModalVisible(false);
        showAlertMessage('Success', 'Transfer offer submitted successfully!');
      }
    } catch (error) {
      logger.error('Error submitting transfer offer/bid:', error);
      showAlertMessage(
        'Error',
        `Failed to submit ${isAuctionPlayer ? 'bid' : 'transfer offer'}. Please try again.`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReleasePlayer = async (player: Player, onClose: () => void) => {
    if (!userTeam || !manager) {
      showAlertMessage('Error', 'Unable to release player. Team information not available.');
      return;
    }

    setIsReleasing(true);
    try {
      const response = await callApi<{
        error?: string;
        message: string;
        playerId: string;
        playerName: string;
      }>('/transfer/release-player', {
        method: 'POST',
        body: JSON.stringify({
          playerId: player.playerId,
          teamId: userTeam.teamId,
        }),
      });

      if (response.status !== 200) {
        showAlertMessage('Error', response.error || 'Unable to release player.');
        return;
      }

      // Update the team in cache by removing the player
      const updatedPlayers = userTeam.players.filter((p) => p.playerId !== player.playerId);
      updateTeam({
        players: updatedPlayers,
      });

      setIsReleaseModalVisible(false);
      showAlertMessage(
        'Success',
        response.message || `${player.firstName} ${player.surname} has been released successfully!`
      );
      setAlertCloseAction(goBack);
      onClose();
    } catch (error) {
      logger.error('Error releasing player:', error);
      setIsReleaseModalVisible(false);
      showAlertMessage('Error', 'Failed to release player. Please try again.');
    } finally {
      setIsReleasing(false);
    }
  };

  const prefillOfferAmount = (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number
  ) => {
    if (isAuctionPlayer && currentBid) {
      // For auctions, prefill with minimum bid (current bid + 1000)
      setOfferAmount((currentBid + 1000).toString());
    } else {
      // For transfers, prefill with player value
      setOfferAmount(player.value.toFixed(0).toString());
    }
  };

  const state: PlayerActionsState = {
    isOfferModalVisible,
    offerAmount,
    isSubmitting,
    showAlert,
    alertCloseAction,
    alertMessage,
    maxBid,
    isHighestBidder,
    isReleaseModalVisible,
    isReleasing,
  };

  const actions: PlayerActionsActions = {
    setIsOfferModalVisible,
    setOfferAmount,
    setShowAlert,
    setAlertCloseAction,
    setIsReleaseModalVisible,
    handleSubmitOffer,
    handleReleasePlayer,
    prefillOfferAmount,
  };

  return [state, actions];
};
