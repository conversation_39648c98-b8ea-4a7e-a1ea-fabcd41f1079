import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';

import { TransferListPlayer } from '../../models/player';

interface StyledProps {
  theme: DefaultTheme;
}

interface BidHistoryProps {
  transferListPlayer: TransferListPlayer;
}

const BidHistoryContainer = styled.View`
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
`;

const BidHistoryHeader = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 8px 12px;
`;

const BidHistoryHeaderText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

const BidHistoryRow = styled.View`
  flex-direction: row;
  padding: 12px;
  border-bottom-width: 1px;
  border-bottom-color: #e0e0e0;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const BidHistoryTeamName = styled(Text)`
  flex: 2;
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
`;

const BidHistoryAmount = styled(Text)`
  flex: 1;
  font-size: 14px;
  text-align: right;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

export const BidHistory: React.FC<BidHistoryProps> = ({ transferListPlayer }) => {
  return (
    <BidHistoryContainer>
      <BidHistoryHeader>
        <BidHistoryHeaderText>Bid History</BidHistoryHeaderText>
      </BidHistoryHeader>

      {/* Initial Price Row */}
      <BidHistoryRow>
        <BidHistoryTeamName>Initial Price</BidHistoryTeamName>
        <BidHistoryAmount>
          {formatPlayerValue(transferListPlayer.auctionStartPrice)}
        </BidHistoryAmount>
      </BidHistoryRow>

      {/* Bid History Rows */}
      {transferListPlayer.bidHistory && transferListPlayer.bidHistory.length > 0 ? (
        // Sort bids by bidTime (oldest first)
        [...transferListPlayer.bidHistory]
          .sort((a, b) => a.bidTime - b.bidTime)
          .map((bid, index) => {
            // If maximum bid is higher than current price, show current price instead
            const displayBidAmount =
              bid.maximumBid > transferListPlayer.auctionCurrentPrice
                ? transferListPlayer.auctionCurrentPrice
                : bid.maximumBid;

            return (
              <BidHistoryRow key={`bid-${index}`}>
                <BidHistoryTeamName>{bid.teamName}</BidHistoryTeamName>
                <BidHistoryAmount>{formatPlayerValue(displayBidAmount)}</BidHistoryAmount>
              </BidHistoryRow>
            );
          })
      ) : (
        <BidHistoryRow>
          <BidHistoryTeamName>No bids yet</BidHistoryTeamName>
          <BidHistoryAmount></BidHistoryAmount>
        </BidHistoryRow>
      )}
    </BidHistoryContainer>
  );
};
