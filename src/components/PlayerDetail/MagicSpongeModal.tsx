import React from 'react';
import { Image, Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Player } from '../../models/player';
import { useRewardAd } from '../Ads';
import { Text } from '../Text';
import { useMagicSponge } from './hooks/useMagicSponge';
import { usePlayerActions } from './hooks/usePlayerActions';

interface StyledProps {
  theme: DefaultTheme;
}

interface MagicSpongeModalProps {
  player: Player;
  isInjured: boolean;
  spongesAvailable: number;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitleRow = styled.View`
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: left;
  flex: 1;
`;

const SpongePill = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: #f0c419;
  border-radius: 16px;
  padding: 4px 12px;
  margin-left: 8px;
  align-self: flex-start;
`;

const SpongePillText = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 16px;
  color: #333;
  margin-left: 6px;
`;

const DetailText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-bottom: 8px;
`;

const ModalButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 8px;
`;

const ModalButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  flex: 1;
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  margin-top: 16px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

export const MagicSpongeModal: React.FC<MagicSpongeModalProps> = ({
  player,
  isInjured,
  spongesAvailable,
}) => {
  const { isRewardAdLoaded, showRewardAd } = useRewardAd();
  const [, playerActions] = usePlayerActions();
  const [magicSpongeState, magicSpongeActions] = useMagicSponge();

  const handleMagicSpongeUse = () => {
    magicSpongeActions.useMagicSponge(player as Player, (_title, _message) => {
      playerActions.setShowAlert(true);
    });
  };

  const spongeReason = isInjured
    ? `Use the magic sponge to reduce the injury duration by 1 day`
    : `Use the magic sponge to restore ${player.firstName} ${player.surname} to full energy`;

  return (
    <Modal
      visible={magicSpongeState.isSpongeModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => magicSpongeActions.setIsSpongeModalVisible(false)}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitleRow>
            <ModalTitle>Magic Sponge</ModalTitle>
            <SpongePill>
              <Image
                source={require('../../../assets/magicSponge.png')}
                style={{ width: 24, height: 24 }}
              />
              <SpongePillText>{spongesAvailable}</SpongePillText>
            </SpongePill>
          </ModalTitleRow>
          <DetailText>{spongeReason}</DetailText>
          <ModalButtonContainer>
            <ModalButton
              disabled={spongesAvailable === 0 || magicSpongeState.isSpongeLoading}
              onPress={handleMagicSpongeUse}
              style={{
                opacity: spongesAvailable === 0 || magicSpongeState.isSpongeLoading ? 0.5 : 1,
              }}
            >
              <ButtonText>
                {magicSpongeState.isSpongeLoading ? 'Using...' : 'Use magic sponge'}
              </ButtonText>
            </ModalButton>
            <ModalButton
              disabled={!isRewardAdLoaded}
              onPress={showRewardAd}
              style={{ opacity: !isRewardAdLoaded ? 0.5 : 1 }}
            >
              <ButtonText>Watch ad</ButtonText>
            </ModalButton>
            <ModalButton onPress={() => {}}>
              <ButtonText>Buy more</ButtonText>
            </ModalButton>
          </ModalButtonContainer>
          <TransferButton
            onPress={() => magicSpongeActions.setIsSpongeModalVisible(false)}
            style={{ marginTop: 16 }}
          >
            <ButtonText>Close</ButtonText>
          </TransferButton>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
