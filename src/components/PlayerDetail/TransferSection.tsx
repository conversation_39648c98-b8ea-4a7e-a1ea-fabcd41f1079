import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';
import { BidHistory } from './BidHistory';
import { useAuctionTimer } from './hooks/useAuctionTimer';

import { usePlayerUtils } from '../../context/PlayerContext';
import { TransferListPlayer } from '../../models/player';
import { usePlayerActions } from './hooks/usePlayerActions';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface StyledProps {
  theme: DefaultTheme;
}

interface TransferSectionProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
  maxBid: number | null;
  isHighestBidder: boolean;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 24px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

export const TransferSection: React.FC<TransferSectionProps> = ({
  player,
  playerStatus,
  isPlayerInUserTeam,
  isAuctionPlayer,
  maxBid,
  isHighestBidder,
}) => {
  const currentBid = player.bidHistory?.length
    ? Math.max(...(player.bidHistory || []).map((bid) => bid.maximumBid))
    : player.auctionStartPrice || 0;

  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';
  const { playerActionsState, playerActions } = usePlayerUtils();
  const { timeRemaining, auctionCompleted } = useAuctionTimer(player);

  const handleTransferPress = () => {
    playerActions.prefillOfferAmount(
      player as TransferListPlayer,
      playerStatus.isAuctionPlayer,
      currentBid
    );
    playerActions.setIsOfferModalVisible(true);
  };

  if (isPlayerInUserTeam) {
    return (
      <TransferButton
        onPress={() => playerActions.setIsReleaseModalVisible(true)}
        style={{ backgroundColor: '#e3172a' }}
      >
        <ButtonText>Release Player</ButtonText>
      </TransferButton>
    );
  }

  return (
    <>
      {isAuctionPlayer && (
        <StatusContainer style={{ backgroundColor: '#2E7D32' }}>
          <StatusText>
            Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
          </StatusText>
          <StatusText>{timeRemaining}</StatusText>
          {maxBid && (
            <StatusText
              style={{
                color: isHighestBidder ? '#FFEB3B' : '#FFFFFF',
                fontFamily: 'NunitoBold',
              }}
            >
              {isHighestBidder
                ? '✓ You are the highest bidder!'
                : '✗ You are not the highest bidder'}
            </StatusText>
          )}
        </StatusContainer>
      )}

      {/* Only show Bid/Offer button if auction is not completed */}
      {!(isAuctionPlayer && auctionCompleted) && (
        <TransferButton onPress={handleTransferPress}>
          <ButtonText>{player.teamId === '' ? 'Bid Now' : 'Submit Transfer Offer'}</ButtonText>
        </TransferButton>
      )}

      {/* Bid History Section */}
      {isAuctionPlayer && <BidHistory transferListPlayer={player} />}
    </>
  );
};
