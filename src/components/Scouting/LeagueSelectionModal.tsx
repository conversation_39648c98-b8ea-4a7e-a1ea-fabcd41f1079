import React, { useMemo } from 'react';
import { FlatList, Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { League } from '../../models/league';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface SelectableItemProps extends StyledProps {
  isSelected: boolean;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-height: 80%;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const TierHeader = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 18px;
  padding: 8px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
`;

const LeagueItem = styled.TouchableOpacity<SelectableItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '40' : 'transparent'};
`;

const LeagueName = styled(Text)<SelectableItemProps>`
  font-family: ${(props) => (props.isSelected ? 'NunitoBold' : 'Nunito-Regular')};
  font-size: 16px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const Button = styled.TouchableOpacity`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
`;

const ButtonText = styled(Text)`
  color: ${(props: StyledProps) => props.theme.colors.background};
  font-family: 'NunitoBold';
`;

const CostRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  gap: 12px;
`;

const CostIcon = styled.Image`
  width: 24px;
  height: 24px;
  margin-right: 4px;
`;

const CostText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

interface Props {
  visible: boolean;
  leagues: League[];
  onClose: () => void;
  onSelect: (leagueId: string) => void;
}

const LeagueSelectionModal: React.FC<Props> = ({ visible, leagues, onClose, onSelect }) => {
  const [selectedLeagueId, setSelectedLeagueId] = React.useState<string | null>(null);

  const groupedLeagues = useMemo(() => {
    if (!leagues || !Array.isArray(leagues)) {
      return [];
    }

    const grouped = leagues.reduce(
      (acc, league) => {
        if (!acc[league.tier]) {
          acc[league.tier] = [];
        }
        acc[league.tier].push(league);
        return acc;
      },
      {} as Record<number, League[]>
    );

    return Object.entries(grouped)
      .sort(([tierA], [tierB]) => Number(tierA) - Number(tierB))
      .flatMap(([tier, leagueList]) => [
        { type: 'header', tier: Number(tier) },
        ...leagueList.map((league) => ({ type: 'league', ...league })),
      ]);
  }, [leagues]);

  const renderItem = ({ item }: { item: any }) => {
    if (item.type === 'header') {
      return <TierHeader>Tier {item.tier}</TierHeader>;
    }

    return (
      <LeagueItem
        isSelected={selectedLeagueId === item.id}
        onPress={() => setSelectedLeagueId(item.id)}
      >
        <LeagueName isSelected={selectedLeagueId === item.id}>{item.name}</LeagueName>
      </LeagueItem>
    );
  };

  const handleConfirm = () => {
    if (selectedLeagueId) {
      onSelect(selectedLeagueId);
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Select League to Scout</ModalTitle>

          <CostRow>
            <CostIcon source={require('../../../assets/scoutToken.png')} />
            <CostText>x 1</CostText>
            <CostIcon source={require('../../../assets/icon.png')} />
            <CostText>x 5,000</CostText>
          </CostRow>

          <FlatList
            data={groupedLeagues}
            renderItem={renderItem}
            keyExtractor={(item) =>
              item.type === 'header' ? `tier-${item.tier}` : (item as League).id
            }
          />
          <ButtonContainer>
            <Button onPress={onClose}>
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button
              onPress={handleConfirm}
              style={{ opacity: selectedLeagueId ? 1 : 0.5 }}
              disabled={!selectedLeagueId}
            >
              <ButtonText>Scout!</ButtonText>
            </Button>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};

export default LeagueSelectionModal;
