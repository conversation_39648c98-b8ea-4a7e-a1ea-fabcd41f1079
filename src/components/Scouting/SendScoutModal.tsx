import React, { useEffect, useState } from 'react';
import { callApi } from '../../api/client';
import { useDataCache } from '../../context/DataCacheContext';
import { useManager } from '../../context/ManagerContext';
import { League } from '../../models/league';
import { CrossPlatformAlert } from '../CrossPlatformAlert';
import LeagueSelectionModal from './LeagueSelectionModal';
import TeamSelectionModal from './TeamSelectionModal';

const SCOUT_COST = 5000;

interface SendScoutModalProps {
  state: 'league' | 'team' | null;
  leagues?: League[];
}

interface RequestScoutingResponse {
  remainingTokens: number;
  message: string;
  newBalance: number;
  error?: string;
}

export const SendScoutModal = ({ state, leagues }: SendScoutModalProps) => {
  const { manager } = useManager();
  const { setManager } = useDataCache();

  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [isLeagueModalVisible, setLeagueModalVisible] = useState(false);
  const [isTeamModalVisible, setTeamModalVisible] = useState(false);
  const [showScoutingConfirmation, setShowScoutingConfirmation] = useState(false);

  useEffect(() => {
    switch (state) {
      case 'league':
        setLeagueModalVisible(true);
        break;
      case 'team':
        setTeamModalVisible(true);
        break;
      default:
        setLeagueModalVisible(false);
        setTeamModalVisible(false);
    }
  }, [state]);

  const handleLeagueSelect = async (leagueId: string) => {
    if ((manager?.scoutTokens ?? 0) < 1) {
      setErrorDetails('You do not have enough scout tokens.');
      return;
    }
    if ((manager?.team?.balance ?? 0) < SCOUT_COST) {
      setErrorDetails('You do not have enough funds to send a scout.');
      return;
    }
    try {
      setIsLoading(true);
      const response = await callApi<RequestScoutingResponse>('/scouting/request', {
        method: 'POST',
        body: JSON.stringify({
          type: 'league',
          id: leagueId,
        }),
      });
      if (response.status !== 200) {
        setErrorDetails(response.error || 'An error occurred while scouting the league.');
        return;
      }
      // Decrement scout token and balance on success
      if (manager) {
        manager.scoutTokens = response.remainingTokens;
        manager.team!.balance = response.newBalance;
        setManager(manager); // Update the manager in cache
      }
      setLeagueModalVisible(false);
      setShowScoutingConfirmation(true);
    } catch (e) {
      setErrorDetails((e as Error).message || 'An error occurred while scouting the league.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTeamSelect = async (teamId: string) => {
    if ((manager?.scoutTokens ?? 0) < 1) {
      setErrorDetails('You do not have enough scout tokens.');
      return;
    }
    if ((manager?.team?.balance ?? 0) < SCOUT_COST) {
      setErrorDetails('You do not have enough funds to send a scout.');
      return;
    }
    try {
      setIsLoading(true);
      const response = await callApi<RequestScoutingResponse>('/scouting/request', {
        method: 'POST',
        body: JSON.stringify({
          type: 'team',
          id: teamId,
        }),
      });
      if (response.status !== 200) {
        setErrorDetails(response.error || 'An error occurred while scouting the team.');
        return;
      }
      // Decrement scout token and balance on success
      if (manager) {
        manager.scoutTokens = response.remainingTokens;
        manager.team!.balance = response.newBalance;
        setManager(manager); // Update the manager in cache
      }
      setTeamModalVisible(false);
      setShowScoutingConfirmation(true);
    } catch (e) {
      setErrorDetails((e as Error).message || 'An error occurred while scouting the league.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <LeagueSelectionModal
        visible={isLeagueModalVisible}
        leagues={leagues || []}
        onClose={() => setLeagueModalVisible(false)}
        onSelect={handleLeagueSelect}
      />

      <TeamSelectionModal
        visible={isTeamModalVisible}
        leagues={leagues || []}
        onClose={() => setTeamModalVisible(false)}
        onSelect={handleTeamSelect}
      />

      <CrossPlatformAlert
        visible={showScoutingConfirmation}
        title="Success"
        message="Your scout has packed his bags and is off to look for players who know their left from their right."
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowScoutingConfirmation(false),
          },
        ]}
        onDismiss={() => setShowScoutingConfirmation(false)}
      />

      <CrossPlatformAlert
        visible={errorDetails !== null}
        title="Error"
        message={errorDetails ?? ''}
        buttons={[
          {
            text: 'OK',
            onPress: () => setErrorDetails(null),
          },
        ]}
        onDismiss={() => setErrorDetails(null)}
      />
    </>
  );
};
