import { MaterialIcons } from '@expo/vector-icons';
import { NativeStackHeaderProps } from '@react-navigation/native-stack';
import React from 'react';
import { Image } from 'react-native';
import styled from 'styled-components/native';
import { Text } from './Text';

const HeaderContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: #1976d2;
  height: 56px;
  padding-horizontal: 16px;
`;

const HeaderTitle = styled(Text)`
  color: #ffffff;
  font-family: 'NunitoBold';
  font-size: 20px;
  letter-spacing: 3px;
  flex: 1;
`;

const IconContainer = styled.View`
  margin-right: 10px;
`;

const BackButton = styled.TouchableOpacity`
  margin-right: 10px;
`;

export function AppHeader({ navigation, options, back }: NativeStackHeaderProps) {
  return (
    <HeaderContainer>
      {back ? (
        <BackButton onPress={navigation.goBack}>
          <MaterialIcons name="arrow-back" size={24} color="#ffffff" />
        </BackButton>
      ) : (
        <IconContainer>
          <Image
            source={require('../assets/logo.png')}
            style={{
              resizeMode: 'contain',
              marginBottom: 0,
              alignSelf: 'center',
            }}
          />
        </IconContainer>
      )}
      <HeaderTitle>{options.title}</HeaderTitle>
    </HeaderContainer>
  );
}

export const headerStyle = {
  backgroundColor: '#1976d2',
};

export const headerTitleStyle = {
  color: '#ffffff',
  fontFamily: 'NunitoBold',
  fontWeight: '700', // Changed from string to valid fontWeight value
  letterSpacing: 3,
} as const;
