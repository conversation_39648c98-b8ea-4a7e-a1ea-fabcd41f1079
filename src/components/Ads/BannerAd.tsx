import React, { useEffect, useState } from 'react';
import { BannerAdSize, BannerAd as MobileBannerAd, TestIds } from 'react-native-google-mobile-ads';
import { logger } from '../../utils/logger';
import { StyledSafeAreaView } from './StyledSafeAreaView';

import styled from 'styled-components/native';
import { initAds } from './initAds';

// Use test ad unit ID in development, and real ad unit ID in production
const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-3959534729713487/1389516945';

// Add this styled component
const HiddenView = styled.View`
  width: 0;
  height: 0;
  overflow: hidden;
`;

export const BannerAd: React.FC = () => {
  const [isAdLoaded, setIsAdLoaded] = useState(false);
  const [adFailed, setAdFailed] = useState(false);

  useEffect(() => {
    initAds();
  }, []);

  if (adFailed) {
    // Render nothing if ad is not loaded or failed
    return null;
  }

  return (
    <>
      {isAdLoaded ? (
        <StyledSafeAreaView edges={['bottom']}>
          <MobileBannerAd
            unitId={adUnitId}
            size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: true,
            }}
            onAdLoaded={() => {
              setAdFailed(false);
              setIsAdLoaded(true);
              logger.log('Ad loaded');
            }}
            onAdFailedToLoad={(error) => {
              setAdFailed(true);
              logger.error('Ad failed to load: ', error);
            }}
          />
        </StyledSafeAreaView>
      ) : (
        // Render off-screen to trigger loading
        <HiddenView>
          <MobileBannerAd
            unitId={adUnitId}
            size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: true,
            }}
            onAdLoaded={() => {
              setAdFailed(false);
              setIsAdLoaded(true);
              logger.log('Ad loaded');
            }}
            onAdFailedToLoad={(error) => {
              setAdFailed(true);
              logger.error('Ad failed to load: ', error);
            }}
          />
        </HiddenView>
      )}
    </>
  );
};

export default BannerAd;
