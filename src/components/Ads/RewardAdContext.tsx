import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { RewardedAd, RewardedAdEventType, TestIds } from 'react-native-google-mobile-ads';
import { useManager } from '../../context/ManagerContext';
import { initAds } from './initAds';

const adUnitId = __DEV__ ? TestIds.REWARDED : 'ca-app-pub-3959534729713487/**********';

interface RewardAdContextProps {
  isRewardAdLoaded: boolean;
  showRewardAd: () => void;
}

const RewardAdContext = createContext<RewardAdContextProps | undefined>(undefined);

export const useRewardAd = () => {
  const context = useContext(RewardAdContext);
  if (!context) {
    throw new Error('useRewardAd must be used within a RewardAdProvider');
  }
  return context;
};

export const RewardAdProvider = ({ children }: { children: ReactNode }) => {
  const { manager } = useManager();

  const [rewarded, setRewarded] = useState<RewardedAd | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    initAds();
  }, []);

  useEffect(() => {
    if (!manager?.managerId) return;

    const ad = RewardedAd.createForAdRequest(adUnitId, {
      keywords: ['football', 'soccer', 'manager', 'sports', 'game'],
      serverSideVerificationOptions: {
        userId: manager.managerId,
      },
    });
    setRewarded(ad);

    const unsubscribeLoaded = ad.addAdEventListener(RewardedAdEventType.LOADED, () => {
      setIsLoaded(true);
    });
    const unsubscribeEarned = ad.addAdEventListener(
      RewardedAdEventType.EARNED_REWARD,
      (payload) => {
        console.log('User earned reward of ', payload);
        setIsLoaded(false);
        ad.load();
      }
    );
    ad.load();

    return () => {
      unsubscribeLoaded();
      unsubscribeEarned();
    };
  }, [manager?.managerId]);

  const showRewardAd = () => {
    if (isLoaded && rewarded) {
      rewarded.show();
    }
  };

  return (
    <RewardAdContext.Provider value={{ isRewardAdLoaded: isLoaded, showRewardAd }}>
      {children}
    </RewardAdContext.Provider>
  );
};
