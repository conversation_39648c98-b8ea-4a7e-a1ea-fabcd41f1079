import React, { createContext, ReactNode, useContext } from 'react';

interface RewardAdContextProps {
  isRewardAdLoaded: boolean;
  showRewardAd: () => void;
}

const RewardAdContext = createContext<RewardAdContextProps | undefined>(undefined);

export const useRewardAd = () => {
  const context = useContext(RewardAdContext);
  if (!context) {
    throw new Error('useRewardAd must be used within a RewardAdProvider');
  }
  return context;
};

export const RewardAdProvider = ({ children }: { children: ReactNode }) => (
  <RewardAdContext.Provider value={{ isRewardAdLoaded: false, showRewardAd: () => {} }}>
    {children}
  </RewardAdContext.Provider>
);
