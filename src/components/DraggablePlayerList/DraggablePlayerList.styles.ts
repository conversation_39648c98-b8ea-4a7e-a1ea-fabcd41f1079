/**
 * DraggablePlayerList Styles
 * 
 * This file contains styled-components for the DraggablePlayerList component.
 */

import { Dimensions, StyleSheet } from 'react-native';
import styled from 'styled-components/native';
import Animated from 'react-native-reanimated';

/**
 * ListContainer - The main container for the entire list
 * 
 * This container takes up the full height and width of its parent.
 */
export const ListContainer = styled.View`
  flex: 1;
  width: 100%;
`;

/**
 * AnimatedScrollView - The scrollable container for list items
 * 
 * This is an Animated.ScrollView that contains all the list items.
 * It's styled to take up the full width of its container.
 */
export const AnimatedScrollView = styled(Animated.ScrollView)`
  width: 100%;  /* Full width to ensure consistent sizing */
`;

// Calculate screen dimensions for consistent sizing across the app
const { width: SCREEN_WIDTH } = Dimensions.get('window');

/**
 * Legacy StyleSheet styles
 * 
 * These styles are kept for backward compatibility with components
 * that might still be using the StyleSheet approach instead of styled-components.
 */
export const styles = StyleSheet.create({
  listContainer: { 
    flex: 1,
    width: '100%' 
  },
});
