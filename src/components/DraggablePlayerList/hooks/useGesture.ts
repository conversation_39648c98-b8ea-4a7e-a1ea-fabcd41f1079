/**
 * useGesture Hook for Player List
 *
 * This hook is the core of the draggable player list functionality. It handles:
 * - Detecting and responding to drag gestures
 * - Calculating item positions during dragging
 * - Animating items when they're reordered
 * - Triggering auto-scrolling when dragging near list edges
 * - Applying visual effects during dragging (scaling, shadows, etc.)
 */

import { Platform } from 'react-native';
import { Gesture } from 'react-native-gesture-handler';
import {
  SharedValue,
  interpolate,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { logger } from '../../../utils/logger';
import {
  CARD_MARGIN,
  EDGE_THRESHOLD,
  MIN_BOUNDRY,
  PAGE_HEADER_OFFSET,
  PLAYER_CARD_HEIGHT,
  SCREEN_HEIGHT,
} from '../constants';
import { NullableNumber, NullableString, TPlayerItem, TPlayerPositions } from '../types';

/**
 * useGesture - Custom hook for handling drag gestures in the player list
 */
export const useGesture = (
  item: TPlayerItem,
  isDragging: SharedValue<number>,
  draggedItemId: SharedValue<NullableString>,
  currentPlayerPositions: SharedValue<TPlayerPositions>,
  scrollUp: () => void,
  scrollDown: () => void,
  scrollY: SharedValue<number>,
  isDragInProgress: SharedValue<boolean>
) => {
  // Shared values for tracking indices during reordering
  const newIndex = useSharedValue<NullableNumber>(null); // Target index when swapping items
  const currentIndex = useSharedValue<NullableNumber>(null); // Current index of this item

  // Derived values that track changes to shared values
  const currentPlayerPositionsDerived = useDerivedValue(() => {
    return currentPlayerPositions.value;
  });

  // The vertical position of this item (in pixels from the top of the list)
  const top = useSharedValue(currentPlayerPositions.value[item.playerId]?.updatedTop || 0);

  // Track whether any dragging is happening in the list
  const isDraggingDerived = useDerivedValue(() => {
    return isDragging.value;
  });

  // Track which item is currently being dragged
  const draggedItemIdDerived = useDerivedValue(() => {
    return draggedItemId.value;
  });

  // Track the current scroll position of the list
  const scrollYDerived = useDerivedValue(() => {
    return scrollY.value;
  });

  // Check if this specific item is the one being dragged
  const isCurrentDraggingItem = useDerivedValue(() => {
    return draggedItemIdDerived.value === item.playerId;
  });

  // Track the total Y position (scroll + touch) during dragging
  const totalY = useSharedValue(0);

  /**
   * getKeyOfValue - Helper function to find a player ID by its index
   */
  const getKeyOfValue = (value: number, obj: TPlayerPositions): string | undefined => {
    'worklet';
    for (const [key, val] of Object.entries(obj)) {
      if (val.updatedIndex === value) {
        return key;
      }
    }
    return undefined;
  };

  /**
   * onGestureUpdate - Handles updates during dragging
   */
  const onGestureUpdate = (newTop: number) => {
    'worklet';

    if (currentIndex.value === null || newTop <= MIN_BOUNDRY) {
      return;
    }

    top.value = newTop;

    if (Platform.OS === 'web') {
      // Web-specific edge detection
      const absolutePosition = newTop;
      const isNearTop = absolutePosition < EDGE_THRESHOLD;
      const isNearBottom = absolutePosition > SCREEN_HEIGHT - EDGE_THRESHOLD - PAGE_HEADER_OFFSET;

      logger.log('Web edge detection', {
        absolutePosition,
        screenHeight: SCREEN_HEIGHT,
        headerOffset: PAGE_HEADER_OFFSET,
        isNearTop,
        isNearBottom,
      });

      if (isNearTop) scrollUp();
      if (isNearBottom) scrollDown();
    } else {
      // Native platforms edge detection
      const topEdge = scrollYDerived.value + EDGE_THRESHOLD;
      const bottomEdge = scrollYDerived.value + SCREEN_HEIGHT - EDGE_THRESHOLD;

      const isUpperEdge = newTop <= topEdge;
      const isBottomEdge = newTop >= bottomEdge;

      logger.log('Native edge detection', {
        newTop,
        topEdge,
        bottomEdge,
        isUpperEdge,
        isBottomEdge,
      });

      if (isUpperEdge) scrollUp();
      if (isBottomEdge) scrollDown();
    }

    newIndex.value = Math.floor(
      (top.value + (PLAYER_CARD_HEIGHT + CARD_MARGIN) / 2) / (PLAYER_CARD_HEIGHT + CARD_MARGIN)
    );
  };

  // React to changes in the player's position
  useAnimatedReaction(
    () => {
      return currentPlayerPositionsDerived.value[item.playerId]?.updatedIndex;
    },
    (currentValue, previousValue) => {
      // Only animate if the index changed and this is not the dragged item
      if (currentValue !== previousValue && currentValue !== undefined) {
        if (!isCurrentDraggingItem.value) {
          top.value = withTiming(
            currentPlayerPositionsDerived.value[item.playerId].updatedIndex *
              (PLAYER_CARD_HEIGHT + CARD_MARGIN),
            { duration: 500 }
          );
        }
      }
    }
  );

  /**
   * gesture - The pan gesture handler for dragging items
   */
  const gesture = Gesture.Pan()
    // Called when the user first touches the item
    .onTouchesDown((e) => {
      // Animate the dragging state to 1 (dragging) with a spring animation
      isDragging.value = withSpring(1);

      // Record which item is being dragged
      draggedItemId.value = item.playerId;

      // Set the flag indicating dragging is in progress
      isDragInProgress.value = true;

      // Store the current index of this item for reordering calculations
      currentIndex.value = currentPlayerPositionsDerived.value[item.playerId].updatedIndex;

      // Calculate and store the total Y position (scroll + touch)
      totalY.value = scrollYDerived.value + e.allTouches[0].absoluteY;
    })
    // Called continuously as the user drags the item
    .onUpdate((e) => {
      // Skip if no item is being dragged
      if (draggedItemIdDerived.value === null) {
        return;
      }

      // Calculate the new total Y position (scroll + current touch position)
      const newTotalY = scrollYDerived.value + e.absoluteY;

      // Calculate how far the finger has moved since the drag started
      const diff = totalY.value - newTotalY;

      // Get the current top position of the dragged item
      const updatedTop = currentPlayerPositionsDerived.value[draggedItemIdDerived.value].updatedTop;

      // Calculate the new top position by adjusting for the finger movement
      const newTop = updatedTop - diff;

      // Update the item's position and handle any reordering
      onGestureUpdate(newTop);
    })
    // Called when the user lifts their finger, ending the drag
    .onTouchesUp(() => {
      // Reset the dragging state flags
      isDragInProgress.value = false;
      draggedItemId.value = null;

      // Animate the dragging visual state back to 0 (not dragging) with a delay
      isDragging.value = withDelay(200, withSpring(0));

      // Skip further processing if we don't have valid indices
      if (newIndex.value === null || currentIndex.value === null) {
        return;
      }

      // Animate the item to its final position based on its new index
      top.value = withSpring(newIndex.value * (PLAYER_CARD_HEIGHT + CARD_MARGIN));

      const currentDragIndexItemKey = getKeyOfValue(
        currentIndex.value,
        currentPlayerPositionsDerived.value
      );

      if (currentDragIndexItemKey !== undefined) {
        //update the values for item whose drag we just stopped
        currentPlayerPositions.value = {
          ...currentPlayerPositionsDerived.value,
          [currentDragIndexItemKey]: {
            ...currentPlayerPositionsDerived.value[currentDragIndexItemKey],
            updatedTop: newIndex.value * (PLAYER_CARD_HEIGHT + CARD_MARGIN),
          },
        };
      }

      //swap the items present at newIndex and currentIndex
      if (newIndex.value !== currentIndex.value) {
        logger.log(`Moved from ${currentIndex.value} to ${newIndex.value}`);
        //find id of the item that currently resides at newIndex
        const newIndexItemKey = getKeyOfValue(newIndex.value, currentPlayerPositionsDerived.value);

        // //find id of the item that currently resides at currentIndex
        // const currentDragIndexItemKey = getKeyOfValue(
        //   currentIndex.value,
        //   currentSongPositionsDerived.value
        // );

        if (newIndexItemKey !== undefined && currentDragIndexItemKey !== undefined) {
          //we update updatedTop and updatedIndex as next time we want to do calculations from new top value and new index
          currentPlayerPositions.value = {
            ...currentPlayerPositionsDerived.value,
            [newIndexItemKey]: {
              ...currentPlayerPositionsDerived.value[newIndexItemKey],
              updatedIndex: currentIndex.value,
              updatedTop: currentIndex.value * (PLAYER_CARD_HEIGHT + CARD_MARGIN),
            },
            [currentDragIndexItemKey]: {
              ...currentPlayerPositionsDerived.value[currentDragIndexItemKey],
              updatedIndex: newIndex.value,
            },
          };

          //update new index as current index
          currentIndex.value = newIndex.value;
        }
      }
    });

  // Create animated styles for the item
  const animatedStyles = useAnimatedStyle(() => {
    return {
      top: top.value,
      width: '100%', // Ensure full width
      transform: [
        {
          scale: isCurrentDraggingItem.value
            ? interpolate(isDraggingDerived.value, [0, 1], [1, 1.025])
            : interpolate(isDraggingDerived.value, [0, 1], [1, 0.98]),
        },
      ],
      zIndex: isCurrentDraggingItem.value ? 1 : 0,
      elevation: isCurrentDraggingItem.value
        ? interpolate(isDraggingDerived.value, [0, 1], [0, 5])
        : 0,
    };
  }, [top.value, isCurrentDraggingItem.value, isDraggingDerived.value]);

  return {
    animatedStyles,
    gesture,
  };
};
