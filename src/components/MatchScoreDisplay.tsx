import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

export interface Scorer {
  playerId: string;
  playerName: string;
  team: number;
  goalTime: {
    minute: number;
    half: number;
  }[];
}

interface Props {
  homeTeamName: string;
  awayTeamName: string;
  score?: [number, number];
  scorers?: Scorer[];
  isLarge?: boolean;
}

const ScoreSection = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
`;

const TeamContainer = styled.View<{ isHome?: boolean }>`
  flex: 1;
  align-items: ${({ isHome }: { isHome?: boolean }) => (isHome ? 'flex-end' : 'flex-start')};
  justify-content: center;
`;

const TeamName = styled(Text)<{ isLarge?: boolean }>`
  font-size: ${({ isLarge }: { isLarge?: boolean }) => (isLarge ? '20px' : '16px')};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const Score = styled.View<{ isLarge?: boolean }>`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: ${({ isLarge }: { isLarge?: boolean }) => (isLarge ? '100px' : '80px')};
  margin: ${({ isLarge }: { isLarge?: boolean }) => (isLarge ? '0 16px' : '0')};
`;

const ScoreText = styled(Text)<{ isLarge?: boolean }>`
  font-size: ${({ isLarge }: { isLarge?: boolean }) => (isLarge ? '32px' : '18px')};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const ScorerText = styled(Text)<{ isHome?: boolean }>`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: ${({ isHome }: { isHome?: boolean }) => (isHome ? 'right' : 'left')};
`;

const formatMatchTime = (minute: number, half: number) => {
  const adjustedMinute = half === 2 ? minute + 45 : minute;
  return `${adjustedMinute}'`;
};

const TeamScorers = ({ scorers, team }: { scorers: Scorer[]; team: number }) => {
  const teamScorers = scorers
    .filter((scorer) => scorer.team === team)
    .map((scorer) => {
      const times = scorer.goalTime
        .map((time) => formatMatchTime(time.minute, time.half))
        .join(', ');
      return `${scorer.playerName} ${times}`;
    });

  return <ScorerText isHome={team === 0}>{teamScorers.join('\n')}</ScorerText>;
};

const ScorerSection = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
`;

const ScorerContainer = styled.View<{ isHome?: boolean }>`
  flex: 1;
  align-items: ${({ isHome }: { isHome?: boolean }) => (isHome ? 'flex-end' : 'flex-start')};
`;

const MatchScoreDisplay: React.FC<Props> = ({
  homeTeamName,
  awayTeamName,
  score,
  scorers,
  isLarge = false,
}) => {
  return (
    <>
      <ScoreSection>
        <TeamContainer isHome>
          <TeamName isLarge={isLarge}>{homeTeamName}</TeamName>
        </TeamContainer>
        <Score isLarge={isLarge}>
          <ScoreText isLarge={isLarge}>{`${score?.[0] || 0} - ${score?.[1] || 0}`}</ScoreText>
        </Score>
        <TeamContainer>
          <TeamName isLarge={isLarge}>{awayTeamName}</TeamName>
        </TeamContainer>
      </ScoreSection>
      {scorers && scorers.length > 0 && (
        <ScorerSection>
          <ScorerContainer isHome>
            <TeamScorers scorers={scorers} team={0} />
          </ScorerContainer>
          <Score isLarge={isLarge} />
          <ScorerContainer>
            <TeamScorers scorers={scorers} team={1} />
          </ScorerContainer>
        </ScorerSection>
      )}
    </>
  );
};

export default MatchScoreDisplay;
