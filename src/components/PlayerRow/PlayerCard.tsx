import { FontAwesome, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, TouchableOpacity, TouchableOpacityProps, View } from 'react-native';
import { GestureDetector } from 'react-native-gesture-handler';
import styled from 'styled-components/native';
import { Player } from '../../models/player';
import {
  calculateCurrentEnergy,
  calculatePlayerPosition,
  formatPlayerValue,
} from '../../utils/PlayerUtils';
import {
  AttributeGroup,
  AttributeLabel,
  AttributeValue,
  AttributesContainer,
  Card,
  CardContent,
  CardHeader,
  DetailButton,
  DraggerContainer,
  DraggerLine,
  EnergyContainer,
  EnergyText,
  PlayerName,
  PlayerPosition,
  PlayerValue,
  StatusIconContainer,
} from './PlayerRowStyles';

interface PlayerCardProps extends TouchableOpacityProps {
  player: Player;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  showValue?: boolean;
  showDragHandle?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  onSelect?: (player: Player) => void;
  dragHandleTestID?: string;
  dragGesture?: any;
  isTeamScreen?: boolean;
}

const StarRatingContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-left: auto;
`;

const PlayerCard: React.FC<PlayerCardProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  showValue = false,
  showDragHandle = false,
  positionFilter = 'All',
  teamAverages = {},
  onSelect,
  //dragHandleTestID = 'drag-handle',
  dragGesture,
  isTeamScreen = false,
  ...props
}) => {
  const positions = calculatePlayerPosition(player);
  const formattedValue = formatPlayerValue(player.value);

  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  // Determine which attributes to show based on position filter
  const getAttributesToShow = () => {
    const { attributes } = player;

    switch (positionFilter) {
      case 'Goalkeeper':
        return [
          { label: 'Ref', value: attributes.reflexes },
          { label: 'Pos', value: attributes.positioning },
          { label: 'Stp', value: attributes.shotStopping },
        ];
      case 'Defender':
        return [
          { label: 'Tck', value: attributes.tackling },
          { label: 'Mrk', value: attributes.marking },
          { label: 'Hdr', value: attributes.heading },
        ];
      case 'Midfielder':
        return [
          { label: 'Pas', value: attributes.passing },
          { label: 'Vis', value: attributes.vision },
          { label: 'Ctl', value: attributes.ballControl },
        ];
      case 'Attacker':
        return [
          { label: 'Fin', value: attributes.finishing },
          { label: 'Pac', value: attributes.pace },
          { label: 'Crs', value: attributes.crossing },
        ];
      default:
        return [];
    }
  };

  const attributesToShow = getAttributesToShow();
  const showAttributes = attributesToShow.length > 0;

  // Check if we're on mobile and update on window resize
  const [isMobile, setIsMobile] = React.useState(Dimensions.get('window').width < 768);

  React.useEffect(() => {
    // Function to update the isMobile state based on window width
    const updateLayout = () => {
      const width = Dimensions.get('window').width;
      setIsMobile(width < 768);
    };

    // Add event listener for window resize
    const dimensionsHandler = Dimensions.addEventListener('change', updateLayout);

    // Clean up event listener on component unmount
    return () => {
      dimensionsHandler.remove();
    };
  }, []);

  // Calculate star rating based on comparison to team average
  const calculateStarRating = () => {
    if (!teamAverages || Object.keys(teamAverages).length === 0) {
      return 0; // No rating if no team averages available
    }

    // Get the attribute keys for the current position filter
    const attributeKeys: string[] = [];

    if (positionFilter === 'Goalkeeper') {
      attributeKeys.push('reflexes', 'positioning', 'shotStopping');
    } else if (positionFilter === 'Defender') {
      attributeKeys.push('tackling', 'marking', 'heading');
    } else if (positionFilter === 'Midfielder') {
      attributeKeys.push('passing', 'vision', 'ballControl');
    } else if (positionFilter === 'Attacker') {
      attributeKeys.push('finishing', 'pace', 'crossing');
    } else {
      // For 'All', compare all attributes
      attributeKeys.push(
        'reflexes',
        'positioning',
        'shotStopping',
        'tackling',
        'marking',
        'heading',
        'passing',
        'vision',
        'ballControl',
        'finishing',
        'pace',
        'crossing'
      );
    }

    // Calculate the average difference between player attributes and team average
    let totalDifference = 0;
    let count = 0;

    attributeKeys.forEach((key) => {
      if (teamAverages[key] && player.attributes[key as keyof typeof player.attributes]) {
        const playerValue = player.attributes[key as keyof typeof player.attributes] as number;
        const teamAverage = teamAverages[key];
        totalDifference += playerValue - teamAverage;
        count++;
      }
    });

    if (count === 0) return 0;

    // Calculate average difference and convert to a 0-10 scale
    // If player equals team average, rating is 5
    // Max attribute is 20, so max difference is 20
    // Scale: -10 to +10 difference maps to 0-10 rating
    const avgDifference = totalDifference / count;
    const rating = 5 + avgDifference / 4; // 4 points difference = 1-star difference

    // Clamp between 0 and 10
    return Math.max(0, Math.min(10, rating));
  };

  const starRating = calculateStarRating();
  const fullStars = Math.floor(starRating / 2);
  const halfStar = starRating % 2 >= 0.5;
  const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

  // Check if player is injured or suspended
  const isInjured = player.injuredUntil && player.injuredUntil > Date.now();
  const isSuspended = player.suspendedForGames > 0;

  // Determine background color based on injury and suspension status
  let cardBackgroundColor = backgroundColor;
  if (isTeamScreen) {
    if (isInjured) {
      cardBackgroundColor = '#e3172a'; // Light red for injured
    } else if (isSuspended) {
      cardBackgroundColor = '#e3172a'; // Light red for suspended
    }
  }

  return (
    <Card
      isSelected={isSelected}
      backgroundColor={cardBackgroundColor}
      isActive={isActive}
      {...props}
    >
      {/* Make the main content area draggable if a gesture is provided */}
      {dragGesture ? (
        <GestureDetector gesture={dragGesture}>
          <CardContent>
            <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
              {showDragHandle && (
                <DraggerContainer>
                  {/* Three horizontal lines forming the drag handle */}
                  <DraggerLine />
                  <DraggerLine />
                  <DraggerLine style={{ marginBottom: 0 }} />
                </DraggerContainer>
              )}
              <PlayerPosition>{positions[0] || 'N/A'}</PlayerPosition>

              {/* Status icons for injured or suspended players - moved to left of player name */}
              {isTeamScreen && (isInjured || isSuspended) && (
                <StatusIconContainer>
                  {isInjured && <MaterialIcons name="healing" size={16} color="#ffffff" />}
                  {isSuspended && (
                    <View
                      style={{
                        position: 'relative',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <MaterialCommunityIcons
                        name="card"
                        size={18}
                        color="white"
                        style={{ position: 'absolute', top: -1, left: -1 }}
                      />
                      <MaterialCommunityIcons name="card" size={16} color="#f44336" />
                    </View>
                  )}
                </StatusIconContainer>
              )}

              <PlayerName
                isMobile={isMobile}
                isUnavailable={isTeamScreen && (isInjured || isSuspended)}
              >
                {`${player.firstName} ${player.surname}`}
              </PlayerName>

              {/* On web, show attributes in the same row */}
              {showAttributes && !isMobile && (
                <AttributesContainer isMobile={isMobile}>
                  {attributesToShow.map((attr, index) => (
                    <AttributeGroup key={index}>
                      <AttributeLabel>{attr.label}:</AttributeLabel>
                      <AttributeValue>{attr.value}</AttributeValue>
                    </AttributeGroup>
                  ))}
                </AttributesContainer>
              )}

              {showValue && !isTeamScreen && <PlayerValue>{formattedValue}</PlayerValue>}

              {isTeamScreen ? (
                <>
                  {/* Energy percentage and player value */}
                  <EnergyContainer>
                    <EnergyText>{`${calculateCurrentEnergy(player.attributes.stamina, player.energy, player.lastMatchPlayed)}%`}</EnergyText>
                    <PlayerValue>{formattedValue}</PlayerValue>
                  </EnergyContainer>
                </>
              ) : (
                <StarRatingContainer>
                  {[...Array(fullStars)].map((_, i) => (
                    <FontAwesome key={`full-${i}`} name="star" size={14} color="#FFD700" />
                  ))}
                  {halfStar && <FontAwesome name="star-half-o" size={14} color="#FFD700" />}
                  {[...Array(emptyStars)].map((_, i) => (
                    <FontAwesome key={`empty-${i}`} name="star-o" size={14} color="#FFD700" />
                  ))}
                </StarRatingContainer>
              )}
            </CardHeader>

            {/* On mobile, show attributes in a second row */}
            {showAttributes && isMobile && (
              <AttributesContainer isMobile={isMobile}>
                {attributesToShow.map((attr, index) => (
                  <AttributeGroup key={index}>
                    <AttributeLabel>{attr.label}:</AttributeLabel>
                    <AttributeValue>{attr.value}</AttributeValue>
                  </AttributeGroup>
                ))}
              </AttributesContainer>
            )}
          </CardContent>
        </GestureDetector>
      ) : (
        <TouchableOpacity onPress={onSelect ? handlePress : undefined} style={{ flex: 1 }}>
          <CardContent>
            <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
              {showDragHandle && (
                <DraggerContainer>
                  {/* Three horizontal lines forming the drag handle */}
                  <DraggerLine />
                  <DraggerLine />
                  <DraggerLine style={{ marginBottom: 0 }} />
                </DraggerContainer>
              )}
              <PlayerPosition>{positions[0] || 'N/A'}</PlayerPosition>

              {/* Status icons for injured or suspended players - moved to left of player name */}
              {isTeamScreen && (isInjured || isSuspended) && (
                <StatusIconContainer>
                  {isInjured && <MaterialIcons name="healing" size={16} color="#ffffff" />}
                  {isSuspended && (
                    <View
                      style={{
                        position: 'relative',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <MaterialCommunityIcons
                        name="card"
                        size={18}
                        color="white"
                        style={{ position: 'absolute', top: -1, left: -1 }}
                      />
                      <MaterialCommunityIcons name="card" size={16} color="#f44336" />
                    </View>
                  )}
                </StatusIconContainer>
              )}

              <PlayerName
                isMobile={isMobile}
                isUnavailable={isTeamScreen && (isInjured || isSuspended)}
              >
                {`${player.firstName} ${player.surname}`}
              </PlayerName>

              {/* On web, show attributes in the same row */}
              {showAttributes && !isMobile && (
                <AttributesContainer isMobile={isMobile}>
                  {attributesToShow.map((attr, index) => (
                    <AttributeGroup key={index}>
                      <AttributeLabel>{attr.label}:</AttributeLabel>
                      <AttributeValue>{attr.value}</AttributeValue>
                    </AttributeGroup>
                  ))}
                </AttributesContainer>
              )}

              {showValue && !isTeamScreen && <PlayerValue>{formattedValue}</PlayerValue>}

              {isTeamScreen ? (
                <>
                  {/* Energy percentage and player value */}
                  <EnergyContainer>
                    <EnergyText>{`${calculateCurrentEnergy(player.attributes.stamina, player.energy, player.lastMatchPlayed)}%`}</EnergyText>
                    <PlayerValue>{formattedValue}</PlayerValue>
                  </EnergyContainer>
                </>
              ) : (
                <StarRatingContainer>
                  {[...Array(fullStars)].map((_, i) => (
                    <FontAwesome key={`full-${i}`} name="star" size={14} color="#FFD700" />
                  ))}
                  {halfStar && <FontAwesome name="star-half-o" size={14} color="#FFD700" />}
                  {[...Array(emptyStars)].map((_, i) => (
                    <FontAwesome key={`empty-${i}`} name="star-o" size={14} color="#FFD700" />
                  ))}
                </StarRatingContainer>
              )}
            </CardHeader>

            {/* On mobile, show attributes in a second row */}
            {showAttributes && isMobile && (
              <AttributesContainer isMobile={isMobile}>
                {attributesToShow.map((attr, index) => (
                  <AttributeGroup key={index}>
                    <AttributeLabel>{attr.label}:</AttributeLabel>
                    <AttributeValue>{attr.value}</AttributeValue>
                  </AttributeGroup>
                ))}
              </AttributesContainer>
            )}
          </CardContent>
        </TouchableOpacity>
      )}

      {/* Detail button on the right side */}
      {onSelect && (
        <DetailButton onPress={handlePress}>
          <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
        </DetailButton>
      )}
    </Card>
  );
};

export default PlayerCard;
