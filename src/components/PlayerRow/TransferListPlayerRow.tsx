import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, TouchableOpacityProps, View } from 'react-native';
import { TransferListPlayer } from '../../hooks/useTransferListPlayers';
import { calculatePlayerPosition } from '../../utils/PlayerUtils';
import PlayerInfo from './PlayerInfo';
import { Card, CardContent, DetailButton, CardHeader, AttributesContainer, AttributeGroup, AttributeLabel, AttributeValue } from './PlayerRowStyles';
import { AuctionInfo } from './SharedComponents';

interface TransferListPlayerRowProps extends TouchableOpacityProps {
  player: TransferListPlayer;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  onSelect?: (player: TransferListPlayer) => void;
  isHighestBidder?: boolean;
}

const TransferListPlayerRow: React.FC<TransferListPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  positionFilter = 'All',
  teamAverages = {},
  onSelect,
  isHighestBidder = false,
  ...props
}) => {
  const positions = calculatePlayerPosition(player);

  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  // Determine which attributes to show based on position filter
  const getAttributesToShow = () => {
    const { attributes } = player;

    switch (positionFilter) {
      case 'Goalkeeper':
        return [
          { label: 'Ref', value: attributes.reflexes },
          { label: 'Pos', value: attributes.positioning },
          { label: 'Stp', value: attributes.shotStopping },
        ];
      case 'Defender':
        return [
          { label: 'Tck', value: attributes.tackling },
          { label: 'Mrk', value: attributes.marking },
          { label: 'Hdr', value: attributes.heading },
        ];
      case 'Midfielder':
        return [
          { label: 'Pas', value: attributes.passing },
          { label: 'Vis', value: attributes.vision },
          { label: 'Ctl', value: attributes.ballControl },
        ];
      case 'Attacker':
        return [
          { label: 'Fin', value: attributes.finishing },
          { label: 'Pac', value: attributes.pace },
          { label: 'Crs', value: attributes.crossing },
        ];
      default:
        return [];
    }
  };

  const attributesToShow = getAttributesToShow();
  const showAttributes = attributesToShow.length > 0;

  // Check screen width to determine if we're on mobile
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;

  // Check if player is injured or suspended
  const isInjured = player.injuredUntil && player.injuredUntil > Date.now();
  const isSuspended = player.suspendedForGames > 0;

  // Determine card background color
  let cardBackgroundColor = backgroundColor;
  if (!cardBackgroundColor) {
    if (isInjured || isSuspended) {
      cardBackgroundColor = '#8B0000'; // Dark red for unavailable players
    } else {
      cardBackgroundColor = undefined; // Use default theme color
    }
  }

  return (
    <Card
      isSelected={isSelected}
      backgroundColor={cardBackgroundColor}
      isActive={isActive}
      {...props}
    >
      <CardContent>
        <PlayerInfo player={player} positionFilter={positionFilter} showImages={true} />
      </CardContent>

      {/* Auction info on the right side */}
      <AuctionInfo player={player} isHighestBidder={isHighestBidder} />

      {/* Detail button on the right side */}
      {onSelect && (
        <DetailButton onPress={handlePress}>
          <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
        </DetailButton>
      )}
    </Card>
  );
};

export default TransferListPlayerRow;
