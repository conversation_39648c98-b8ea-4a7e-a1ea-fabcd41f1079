import { FontAwesome } from '@expo/vector-icons';
import React from 'react';
import { View } from 'react-native';
import styled from 'styled-components/native';
import { TransferListPlayer } from '../../models/player';
import { formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';
import { <PERSON>agger<PERSON>ontainer, DraggerLine, PlayerValue, StyledProps } from './PlayerRowStyles';

export const DragHandle = () => {
  return (
    <DraggerContainer>
      {/* Three horizontal lines forming the drag handle */}
      <DraggerLine />
      <DraggerLine />
      <DraggerLine style={{ marginBottom: 0 }} />
    </DraggerContainer>
  );
};

// Auction-related styled components
export const AuctionInfoContainer = styled.View`
  flex-direction: column;
  align-items: flex-end;
  width: 80px;
`;

export const AuctionPrice = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  text-align: right;
`;

export const AuctionTimeRemaining = styled(Text)<StyledProps>`
  font-size: 12px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: right;
`;

export const BidCount = styled(Text)<StyledProps>`
  font-size: 10px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: right;
`;

// Format time remaining for auction
export const formatTimeRemaining = (endTime: number): string => {
  const now = Date.now();
  const timeLeft = endTime - now;

  if (timeLeft <= 0) {
    return 'Ended';
  }

  // Format time remaining
  const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
  const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
};

// Auction info display component
interface AuctionInfoProps {
  player: TransferListPlayer;
  isHighestBidder?: boolean;
}

export const AuctionInfo: React.FC<AuctionInfoProps> = ({ player, isHighestBidder = false }) => {
  if (player.teamId !== '') {
    return <PlayerValue>{formatPlayerValue(player.value)}</PlayerValue>;
  }

  return (
    <AuctionInfoContainer>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-end',
        }}
      >
        {isHighestBidder && (
          <FontAwesome name="trophy" size={14} color="#FFD700" style={{ marginRight: 4 }} />
        )}
        <AuctionPrice>{formatPlayerValue(player.auctionCurrentPrice)}</AuctionPrice>
      </View>
      <AuctionTimeRemaining>{formatTimeRemaining(player.auctionEndTime)}</AuctionTimeRemaining>
      <BidCount>{`${player.bidHistory.length} bid${player.bidHistory.length !== 1 ? 's' : ''}`}</BidCount>
    </AuctionInfoContainer>
  );
};
