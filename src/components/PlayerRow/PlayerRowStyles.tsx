import { Platform, TouchableOpacity } from 'react-native';
import Animated from 'react-native-reanimated';
import styled, { DefaultTheme } from 'styled-components/native';
import { Color_Pallete, PLAYER_CARD_HEIGHT } from '../DraggablePlayerList/constants';
import { Text } from '../Text';

// Shared interfaces
export interface StyledProps {
  theme: DefaultTheme;
}

export interface StyledCardProps extends StyledProps {
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  isInSection?: boolean;
}

export interface CardHeaderProps {
  hasAttributes?: boolean;
  isMobile?: boolean;
}

export interface PlayerNameProps extends StyledProps {
  isMobile?: boolean;
  isUnavailable?: boolean;
}

export interface AttributesContainerProps {
  isMobile?: boolean;
}

// Shared styled components
export const Card = styled.View<StyledCardProps>`
  background-color: ${(props) => props.backgroundColor || props.theme.colors.surface};
  border-radius: 8px;
  padding: 10px;
  margin: ${(props) => (props.isInSection ? '5px 10px' : '10px 15px')};
  border-width: 2px;
  border-color: ${(props) => (props.isSelected ? props.theme.colors.primary : 'transparent')};
  height: ${PLAYER_CARD_HEIGHT - 13}px;
  flex-direction: row;
  align-items: center; /* Center content vertically */
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.1;
      shadow-radius: 2px;
    `,
    android: `
      elevation: 2;
    `,
  })}
`;

export const CardHeader = styled.View<CardHeaderProps>`
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => (props.hasAttributes && props.isMobile ? '12px' : '0')};
`;

export const PlayerName = styled(Text)<PlayerNameProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-right: ${(props) => (props.isMobile ? '0' : '4px')};
  text-decoration-line: ${(props) => (props.isUnavailable ? 'line-through' : 'none')};
`;

export const PlayerPosition = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  width: 40px;
`;

export const PlayerValue = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  min-width: 80px;
  text-align: right;
  flex: 1;
  align-self: flex-end;
`;

export const CardContent = styled.View`
  flex: 1;
`;

export const DetailButton = styled(TouchableOpacity)`
  width: 40px;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  border-left-width: 1px;
  border-left-color: rgba(255, 255, 255, 0.1);
  margin-left: 8px;
`;

export const DraggerContainer = styled(Animated.View)`
  width: 60px; /* Fixed width for consistent UI */
  align-items: center; /* Center horizontally */
  justify-content: center; /* Center vertically */
`;

export const DraggerLine = styled.View`
  width: 40%; /* Width relative to container */
  height: 3px; /* Thickness of the line */
  background-color: ${Color_Pallete.crystal_white};
  margin-bottom: 6px; /* Space between lines */
  border-radius: 1.5px; /* Rounded ends for visual appeal */
`;

export const StatusIconContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
`;

export const EnergyContainer = styled.View`
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
`;

export const EnergyText = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-right: 4px;
`;

export const AttributesContainer = styled.View<AttributesContainerProps>`
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%;
  //margin-left: ${(props) => (props.isMobile ? '64px' : '72px')};
  margin-top: 4px;
`;

export const AttributeGroup = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 12px;
  margin-bottom: 4px;
`;

export const AttributeLabel = styled(Text)<StyledProps>`
  font-size: 12px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-right: 4px;
`;

export const AttributeValue = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;
