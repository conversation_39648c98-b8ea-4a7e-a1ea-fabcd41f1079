import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';
import { useDataCache } from '../context/DataCacheContext';
import { useManager } from '../context/ManagerContext';
import { Text } from './Text';

const Container = styled.View`
  padding: 16px;
  background-color: ${(props) => props.theme.colors.surface};
  margin: 8px;
  border-radius: 8px;
`;

const Button = styled.TouchableOpacity`
  background-color: ${(props) => props.theme.colors.primary};
  padding: 12px 16px;
  border-radius: 6px;
  margin: 4px 0;
  align-items: center;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: ${(props) => props.theme.typography.bold};
`;

const InfoText = styled(Text)`
  margin: 4px 0;
  font-size: 14px;
`;

/**
 * Example component demonstrating how to use the cache system
 * This shows how to read from cache and update cached data
 */
const CacheExample: React.FC = () => {
  const { cache, updatePlayer, updateManager, findPlayer } = useDataCache();
  const { manager, team, updateManager: updateManagerContext } = useManager();

  // Example: Update a player's energy
  const handleUpdatePlayerEnergy = () => {
    if (team?.players && team.players.length > 0) {
      const firstPlayer = team.players[0];
      updatePlayer({
        playerId: firstPlayer.playerId,
        energy: Math.min(100, firstPlayer.energy + 10), // Add 10 energy, max 100
      });
    }
  };

  // Example: Update manager's magic sponges
  const handleUpdateMagicSponges = () => {
    if (manager) {
      updateManagerContext({
        magicSponges: manager.magicSponges + 1,
      });
    }
  };

  // Example: Find a player in cache
  const handleFindPlayer = () => {
    if (team?.players && team.players.length > 0) {
      const firstPlayer = team.players[0];
      const cachedPlayer = findPlayer(firstPlayer.playerId);
      console.log('Found player in cache:', cachedPlayer);
    }
  };

  return (
    <Container>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 12 }}>
        Cache System Example
      </Text>
      
      <InfoText>Manager: {manager?.firstName} {manager?.lastName}</InfoText>
      <InfoText>Magic Sponges: {manager?.magicSponges || 0}</InfoText>
      <InfoText>Team: {team?.teamName}</InfoText>
      <InfoText>Players in team: {team?.players?.length || 0}</InfoText>
      <InfoText>Players in cache: {cache.players.teamPlayers.length}</InfoText>
      <InfoText>Transfer list players in cache: {cache.players.transferListPlayers.length}</InfoText>
      
      {team?.players && team.players.length > 0 && (
        <>
          <InfoText>
            First player: {team.players[0].firstName} {team.players[0].surname} 
            (Energy: {team.players[0].energy}%)
          </InfoText>
          
          <Button onPress={handleUpdatePlayerEnergy}>
            <ButtonText>Add 10 Energy to First Player</ButtonText>
          </Button>
        </>
      )}
      
      <Button onPress={handleUpdateMagicSponges}>
        <ButtonText>Add 1 Magic Sponge</ButtonText>
      </Button>
      
      <Button onPress={handleFindPlayer}>
        <ButtonText>Find First Player in Cache (Check Console)</ButtonText>
      </Button>
      
      <InfoText style={{ marginTop: 12, fontSize: 12, fontStyle: 'italic' }}>
        This component demonstrates how the cache system works. 
        Changes made here will be reflected immediately in the UI 
        without requiring API calls.
      </InfoText>
    </Container>
  );
};

export default CacheExample;
