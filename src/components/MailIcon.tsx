import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { LAST_READ_MESSAGE_DATE_KEY, LAST_READ_MESSAGE_ID_KEY } from '../context/ManagerContext';
import { useInboxMessages } from '../hooks/useInboxMessages';
import { logger } from '../utils/logger';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

const MailContainer = styled.View`
  position: relative;
  padding: 10px;
`;

const Badge = styled.View`
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: ${(props: StyledProps) => props.theme.colors.error};
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  justify-content: center;
  align-items: center;
  z-index: 1;
`;

const BadgeText = styled(Text)`
  color: white;
  font-size: 12px;
  font-family: 'NunitoBold';
`;

interface MailIconProps {
  size?: number;
  color?: string;
}

export const MailIcon: React.FC<MailIconProps> = ({ size = 24, color = 'black' }) => {
  const { data: messagesData, isLoading } = useInboxMessages();
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    const checkUnreadMessages = async () => {
      if (!messagesData?.messages || messagesData.messages.length === 0) {
        setUnreadCount(0);
        return;
      }

      try {
        // Get the last read message ID and date from storage
        const lastReadMessageId = await AsyncStorage.getItem(LAST_READ_MESSAGE_ID_KEY);
        const lastReadMessageDate = await AsyncStorage.getItem(LAST_READ_MESSAGE_DATE_KEY);

        if (!lastReadMessageId && !lastReadMessageDate) {
          // No messages have been read yet, all are unread
          setUnreadCount(messagesData.messages.length);
          return;
        }

        let unreadMessages = 0;

        if (lastReadMessageDate) {
          // Count messages newer than the last read date
          const lastReadDate = parseInt(lastReadMessageDate, 10);
          unreadMessages = messagesData.messages.filter(
            (message) => message.date > lastReadDate
          ).length;
        } else if (lastReadMessageId) {
          // Find the index of the last read message and count messages after it
          const lastReadIndex = messagesData.messages.findIndex(
            (message) => message.id === lastReadMessageId
          );
          if (lastReadIndex >= 0) {
            unreadMessages = lastReadIndex; // Messages before this index are unread
          } else {
            // Last read message not found, assume all are unread
            unreadMessages = messagesData.messages.length;
          }
        }

        setUnreadCount(unreadMessages);
      } catch (error) {
        logger.error('Error checking unread messages:', error);
        setUnreadCount(0);
      }
    };

    checkUnreadMessages();
  }, [messagesData]);

  const handlePress = () => {
    router.push('/inbox');
  };

  if (isLoading) {
    return (
      <TouchableOpacity onPress={handlePress} style={{ paddingHorizontal: 10 }}>
        <MaterialIcons name="mail" size={size} color={color} />
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity onPress={handlePress} style={{ paddingHorizontal: 10 }}>
      <MailContainer>
        <MaterialIcons name="mail" size={size} color={color} />
        {unreadCount > 0 && (
          <Badge>
            <BadgeText>{unreadCount > 99 ? '99+' : unreadCount.toString()}</BadgeText>
          </Badge>
        )}
      </MailContainer>
    </TouchableOpacity>
  );
};
