import React from 'react';
import { Alert, Modal, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 300px;
`;

const ModalTitle = styled(Text)`
  font-size: 18px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 10px;
  text-align: center;
`;

const ModalMessage = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  margin-bottom: 20px;
  text-align: center;
`;

const ModalButton = styled.TouchableOpacity<{ $isDestructive?: boolean }>`
  padding: 15px;
  margin: 5px 0;
  border-radius: 5px;
  background-color: ${(props: StyledProps & { $isDestructive?: boolean }) =>
    props.$isDestructive ? props.theme.colors.error : props.theme.colors.primary};
`;

const ModalButtonText = styled(Text)`
  color: #ffffff;
  text-align: center;
  font-size: 16px;
`;

export interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface CrossPlatformAlertProps {
  visible: boolean;
  title: string;
  message: string;
  buttons: AlertButton[];
  onDismiss: () => void;
}

export const CrossPlatformAlert: React.FC<CrossPlatformAlertProps> = ({
  visible,
  title,
  message,
  buttons,
  onDismiss,
}) => {
  React.useEffect(() => {
    if (visible && Platform.OS !== 'web') {
      Alert.alert(
        title,
        message,
        buttons.map((button) => ({
          text: button.text,
          onPress: () => {
            button.onPress?.();
            onDismiss();
          },
          style: button.style,
        })),
        { cancelable: true, onDismiss }
      );
    }
  }, [visible]);

  if (Platform.OS !== 'web') {
    return null;
  }

  return (
    <Modal animationType="fade" transparent={true} visible={visible} onRequestClose={onDismiss}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>{title}</ModalTitle>
          <ModalMessage>{message}</ModalMessage>
          {buttons.map((button, index) => (
            <ModalButton
              key={index}
              $isDestructive={button.style === 'destructive'}
              onPress={() => {
                button.onPress?.();
                onDismiss();
              }}
            >
              <ModalButtonText>{button.text}</ModalButtonText>
            </ModalButton>
          ))}
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
