import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { logger } from '../utils/logger';

const TEAM_INPUT_METHOD_KEY = '@team_input_method';

export type TeamInputMethod = 'drag' | 'tap';

interface TeamInputMethodContextType {
  inputMethod: TeamInputMethod;
  setInputMethod: (method: TeamInputMethod) => Promise<void>;
}

const TeamInputMethodContext = createContext<TeamInputMethodContextType>({
  inputMethod: 'tap',
  setInputMethod: async () => {},
});

export const useTeamInputMethod = () => useContext(TeamInputMethodContext);

export const TeamInputMethodProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [inputMethod, setInputMethodState] = useState<TeamInputMethod>('tap');

  useEffect(() => {
    const loadInputMethod = async () => {
      try {
        const savedMethod = await AsyncStorage.getItem(TEAM_INPUT_METHOD_KEY);
        if (savedMethod && (savedMethod === 'drag' || savedMethod === 'tap')) {
          setInputMethodState(savedMethod as TeamInputMethod);
        } else {
          // Set default to tap if no saved preference
          setInputMethodState('tap');
        }
      } catch (error) {
        logger.warn('Failed to load team input method preference:', error);
      }
    };

    loadInputMethod();
  }, []);

  const setInputMethod = async (method: TeamInputMethod) => {
    try {
      await AsyncStorage.setItem(TEAM_INPUT_METHOD_KEY, method);
      setInputMethodState(method);
    } catch (error) {
      logger.warn('Failed to save team input method preference:', error);
    }
  };

  return (
    <TeamInputMethodContext.Provider
      value={{
        inputMethod,
        setInputMethod,
      }}
    >
      {children}
    </TeamInputMethodContext.Provider>
  );
};
