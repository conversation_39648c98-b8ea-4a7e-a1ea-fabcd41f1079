import { Hub } from '@aws-amplify/core';
import { getCurrentUser, signOut } from 'aws-amplify/auth';
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { logger } from '../utils/logger';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuth = useCallback(async () => {
    try {
      const user = await getCurrentUser();
      setIsAuthenticated(!!user);
    } catch {
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    // Listen for auth events
    const listener = Hub.listen('auth', async ({ payload }) => {
      const { event } = payload;
      logger.log('Auth event in AuthProvider:', event);
      if (event === 'signedIn') {
        setIsAuthenticated(true);
        setIsLoading(false);
      }
      if (event === 'signedOut') {
        setIsAuthenticated(false);
        setIsLoading(false);
      }
    });

    return () => {
      listener();
    };
  }, []);

  const logout = async () => {
    try {
      logger.log('Logging out...');
      setIsLoading(true);
      await signOut();
    } catch {
      // Handle error silently or use proper error handling
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, isLoading, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
