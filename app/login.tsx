import {
  Authenticator,
  ThemeProvider,
  defaultDarkModeOverride,
} from '@aws-amplify/ui-react-native';
import { I18n } from 'aws-amplify/utils';
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { ActivityIndicator, Image } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../src/components/Text';
import { useAuth } from '../src/context/AuthContext';
import { useTheme } from '../src/theme/ThemeContext';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 0;
  width: 100%;
`;
styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 32px;
`;
// Custom header for the Authenticator
const AuthHeader = () => {
  return (
    <>
      <Image
        source={require('../assets/logo.png')}
        style={{
          width: 400,
          height: 400,
          resizeMode: 'contain',
          marginBottom: 0,
          alignSelf: 'center',
        }}
      />
    </>
  );
};

// Override the default Authenticator styles
I18n.putVocabulariesForLanguage('en', {
  'Sign In': 'Login',
  'Sign in': 'Login',
  'Sign in to your account': 'Welcome Back!',
});

const LoginScreen = () => {
  const { isLoading, isAuthenticated } = useAuth();
  const { theme: appTheme, isDark } = useTheme();

  // If we're already authenticated and we navigate here, redirect to the app
  useEffect(() => {
    if (isAuthenticated) {
      // Redirect to the app's home screen
      // The app layout will handle checking for notification settings
      router.replace('/');
    }
  }, [isAuthenticated]);

  if (isLoading) {
    return (
      <Container>
        <ActivityIndicator size="large" color={appTheme.colors.primary} />
      </Container>
    );
  }

  return (
    <Container>
      <ThemeProvider
        colorMode={isDark ? 'dark' : 'light'}
        theme={{
          tokens: {
            colors: {
              background: {
                value: appTheme.colors.background,
              },
            },
          },
          overrides: [defaultDarkModeOverride],
        }}
      >
        <Authenticator socialProviders={['google']} Header={AuthHeader} />
      </ThemeProvider>
    </Container>
  );
};

export default LoginScreen;
