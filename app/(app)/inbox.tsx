import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import InboxScreen from '../../src/screens/InboxScreen';

export default function Inbox() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'INBOX',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <MaterialIcons
              name="mail"
              size={40}
              color="black"
              style={{ paddingHorizontal: 10 }}
            />
          ),
        }}
      />
      <InboxScreen />
    </>
  );
}
