import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { Image, TouchableOpacity, View } from 'react-native';
import { MailIcon } from '../../src/components/MailIcon';
import { useAuth } from '../../src/context/AuthContext';
import HomeScreen from '../../src/screens/HomeScreen';

export default function Index() {
  const { logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: '',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <Image
              source={require('../../assets/logo-short.png')}
              style={{
                height: 50,
                width: 160,
                /*resizeMode: 'contain',
                alignSelf: 'left',*/
                marginBottom: 0,
              }}
            />
          ),
          headerRight: () => (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MailIcon size={24} color="black" />
              <TouchableOpacity onPress={handleLogout} style={{ paddingHorizontal: 10 }}>
                <MaterialIcons name="logout" size={24} color="black" />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      <HomeScreen />
    </>
  );
}
