import { FontAwesome } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import NotificationSettingsScreen from '../../src/screens/NotificationSettingsScreen';

export default function NotificationSettings() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'NOTIFICATION SETTINGS',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome name="bell" size={40} color="black" style={{ paddingHorizontal: 10 }} />
          ),
          // Prevent going back to login screen
          headerBackVisible: false,
        }}
      />
      <NotificationSettingsScreen />
    </>
  );
}
