import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { Stack } from 'expo-router';
import LeagueScreen from '../../src/screens/LeagueScreen';
export default function League() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'LEAGUE TABLE',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome6
              name="table-list"
              size={40}
              color="black"
              style={{ paddingHorizontal: 10 }}
            />
          ),
        }}
      />
      <LeagueScreen />
    </>
  );
}
