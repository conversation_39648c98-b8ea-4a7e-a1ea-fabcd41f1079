import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import { PlayerProvider } from '../../src/context/PlayerContext';
import TransfersScreen from '../../src/screens/TransfersScreen';

export default function Transfers() {
  return (
    <>
      <PlayerProvider>
        <Stack.Screen
          options={{
            title: 'TRANSFERS',
            headerTitleStyle: {
              fontFamily: 'NunitoBold',
              fontSize: 20,
            },
            headerLeft: () => (
              <MaterialIcons
                name="swap-horiz"
                size={40}
                color="black"
                style={{ paddingHorizontal: 10 }}
              />
            ),
          }}
        />
        <TransfersScreen />
      </PlayerProvider>
    </>
  );
}
