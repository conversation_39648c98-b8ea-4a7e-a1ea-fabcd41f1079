import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import FinancesScreen from '../../src/screens/FinancesScreen';

export default function Finances() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'FINANCES',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <MaterialIcons
              name="account-balance-wallet"
              size={40}
              color="black"
              style={{ paddingHorizontal: 10 }}
            />
          ),
        }}
      />
      <FinancesScreen />
    </>
  );
}
