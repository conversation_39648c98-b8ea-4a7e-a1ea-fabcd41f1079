import { FontAwesome } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import TeamAssignmentScreen from '../../src/screens/TeamAssignmentScreen';

export default function TeamAssignment() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'TEAM ASSIGNMENT',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome
              name="futbol-o"
              size={40}
              color="black"
              style={{ paddingHorizontal: 10 }}
            />
          ),
          // Prevent going back
          headerBackVisible: false,
        }}
      />
      <TeamAssignmentScreen />
    </>
  );
}
