import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack } from 'expo-router';
import FixturesScreen from '../../src/screens/FixturesScreen';
export default function Fixtures() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'FIXTURES',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome
              name="calendar-o"
              size={40}
              color="black"
              style={{ paddingHorizontal: 10 }}
            />
          ),
        }}
      />
      <FixturesScreen />
    </>
  );
}
