import { FontAwesome } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import ManagerProfileScreen from '../../src/screens/ManagerProfileScreen';

export default function ManagerProfile() {
  return (
    <>
      <Stack.Screen
        options={{
          title: 'MANAGER PROFILE',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome name="user" size={40} color="black" style={{ paddingHorizontal: 10 }} />
          ),
          // Prevent going back to login screen
          headerBackVisible: false,
        }}
      />
      <ManagerProfileScreen />
    </>
  );
}
