import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { Stack, useLocalSearchParams } from 'expo-router';
import FixtureDetailScreen from '../../../src/screens/FixtureDetailScreen';

function FixtureDetail() {
  const { id } = useLocalSearchParams<{ id: string }>();

  return (
    <>
      <Stack.Screen
        options={{
          title: 'MATCH DETAILS',
          headerTitleStyle: {
            fontFamily: 'NunitoBold',
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome6 name="futbol" size={40} color="black" style={{ paddingHorizontal: 10 }} />
          ),
        }}
      />
      <FixtureDetailScreen fixtureId={id} />
    </>
  );
}

export default FixtureDetail;
